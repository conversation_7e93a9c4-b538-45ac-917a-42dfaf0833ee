package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.SalaryProfile;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 薪酬档案Mapper接口
 */
public interface SalaryProfileMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(SalaryProfile record);

    /**
     * 插入记录（选择性）
     */
    int insertSelective(SalaryProfile record);

    /**
     * 根据主键查询
     */
    SalaryProfile selectByPrimaryKey(Long id);

    /**
     * 根据主键更新（选择性）
     */
    int updateByPrimaryKeySelective(SalaryProfile record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(SalaryProfile record);

    /**
     * 根据条件查询列表
     */
    List<SalaryProfile> selectByCondition(@Param("employeeName") String employeeName,
                                         @Param("status") String status,
                                         @Param("tenantId") Long tenantId,
                                         @Param("offset") Integer offset,
                                         @Param("rows") Integer rows);

    /**
     * 根据条件查询总数
     */
    Long countByCondition(@Param("employeeName") String employeeName,
                         @Param("status") String status,
                         @Param("tenantId") Long tenantId);

    /**
     * 根据员工ID查询
     */
    SalaryProfile selectByEmployeeId(@Param("employeeId") Long employeeId,
                                   @Param("tenantId") Long tenantId);

    /**
     * 批量删除
     */
    int batchDeleteByIds(@Param("ids") String ids,
                        @Param("tenantId") Long tenantId,
                        @Param("updater") Long updater,
                        @Param("updateTime") Date updateTime);

    /**
     * 检查员工是否已存在档案
     */
    int checkEmployeeExists(@Param("employeeId") Long employeeId,
                           @Param("tenantId") Long tenantId,
                           @Param("id") Long id);
}
