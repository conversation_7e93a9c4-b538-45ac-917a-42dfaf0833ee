package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.SalaryRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 薪酬记录Mapper接口
 */
public interface SalaryRecordMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(SalaryRecord record);

    /**
     * 插入记录（选择性）
     */
    int insertSelective(SalaryRecord record);

    /**
     * 根据主键查询
     */
    SalaryRecord selectByPrimaryKey(Long id);

    /**
     * 根据主键更新（选择性）
     */
    int updateByPrimaryKeySelective(SalaryRecord record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(SalaryRecord record);

    /**
     * 根据条件查询列表
     */
    List<SalaryRecord> selectByCondition(@Param("employeeName") String employeeName,
                                        @Param("salaryMonth") String salaryMonth,
                                        @Param("status") String status,
                                        @Param("tenantId") Long tenantId,
                                        @Param("offset") Integer offset,
                                        @Param("rows") Integer rows);

    /**
     * 根据条件查询总数
     */
    Long countByCondition(@Param("employeeName") String employeeName,
                         @Param("salaryMonth") String salaryMonth,
                         @Param("status") String status,
                         @Param("tenantId") Long tenantId);

    /**
     * 根据员工ID和月份查询
     */
    SalaryRecord selectByEmployeeAndMonth(@Param("employeeId") Long employeeId,
                                         @Param("salaryMonth") String salaryMonth,
                                         @Param("tenantId") Long tenantId);

    /**
     * 批量删除
     */
    int batchDeleteByIds(@Param("ids") String ids,
                        @Param("tenantId") Long tenantId,
                        @Param("updater") Long updater,
                        @Param("updateTime") Date updateTime);

    /**
     * 更新状态
     */
    int updateStatus(@Param("id") Long id,
                    @Param("status") String status,
                    @Param("tenantId") Long tenantId,
                    @Param("updater") Long updater,
                    @Param("updateTime") Date updateTime);
}
