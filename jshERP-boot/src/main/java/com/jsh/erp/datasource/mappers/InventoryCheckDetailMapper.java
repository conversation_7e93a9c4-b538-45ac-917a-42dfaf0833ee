package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.InventoryCheckDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 库存盘点明细表Mapper接口
 * <AUTHOR>
 * @date 2024-12-23
 */
public interface InventoryCheckDetailMapper {

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(InventoryCheckDetail record);

    /**
     * 选择性插入记录
     */
    int insertSelective(InventoryCheckDetail record);

    /**
     * 根据主键查询
     */
    InventoryCheckDetail selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     */
    int updateByPrimaryKeySelective(InventoryCheckDetail record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(InventoryCheckDetail record);

    /**
     * 根据盘点ID查询明细列表
     */
    List<InventoryCheckDetail> selectByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据盘点ID删除明细
     */
    int deleteByCheckId(@Param("checkId") Long checkId);

    /**
     * 批量插入明细
     */
    int batchInsert(@Param("list") List<InventoryCheckDetail> list);

    /**
     * 批量更新明细
     */
    int batchUpdate(@Param("list") List<InventoryCheckDetail> list);

    /**
     * 根据盘点ID统计明细数量
     */
    int countByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据盘点ID统计差异数量
     */
    int countDiffByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据盘点ID计算总金额
     */
    java.math.BigDecimal sumTotalAmountByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据盘点ID计算差异金额
     */
    java.math.BigDecimal sumDiffAmountByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据商品ID和盘点ID查询明细
     */
    InventoryCheckDetail selectByMaterialAndCheckId(@Param("materialId") Long materialId,
                                                   @Param("checkId") Long checkId);

    /**
     * 根据盘点ID查询有差异的明细
     */
    List<InventoryCheckDetail> selectDiffByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据盘点ID查询盘盈明细
     */
    List<InventoryCheckDetail> selectPositiveDiffByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据盘点ID查询盘亏明细
     */
    List<InventoryCheckDetail> selectNegativeDiffByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据分类统计差异
     */
    List<Map<String, Object>> getDiffStatisticsByCategory(@Param("checkId") Long checkId);

    /**
     * 根据供应商统计差异
     */
    List<Map<String, Object>> getDiffStatisticsBySupplier(@Param("checkId") Long checkId);

    /**
     * 获取差异金额排行
     */
    List<InventoryCheckDetail> getDiffAmountRanking(@Param("checkId") Long checkId,
                                                   @Param("limit") Integer limit);

    /**
     * 获取差异率排行
     */
    List<InventoryCheckDetail> getDiffRateRanking(@Param("checkId") Long checkId,
                                                 @Param("limit") Integer limit);

    /**
     * 根据条件查询明细
     */
    List<InventoryCheckDetail> selectByCondition(@Param("checkId") Long checkId,
                                                @Param("search") String search,
                                                @Param("categoryId") Long categoryId,
                                                @Param("diffType") String diffType);

    /**
     * 更新实盘数量
     */
    int updateActualNumber(@Param("id") Long id,
                          @Param("actualNumber") java.math.BigDecimal actualNumber,
                          @Param("remark") String remark);

    /**
     * 批量更新实盘数量
     */
    int batchUpdateActualNumber(@Param("list") List<InventoryCheckDetail> list);

    /**
     * 根据盘点ID和商品ID列表查询明细
     */
    List<InventoryCheckDetail> selectByCheckIdAndMaterialIds(@Param("checkId") Long checkId,
                                                            @Param("materialIds") List<Long> materialIds);

    /**
     * 根据盘点ID查询未录入的明细
     */
    List<InventoryCheckDetail> selectUnInputByCheckId(@Param("checkId") Long checkId);

    /**
     * 根据盘点ID查询已录入的明细
     */
    List<InventoryCheckDetail> selectInputByCheckId(@Param("checkId") Long checkId);
}
