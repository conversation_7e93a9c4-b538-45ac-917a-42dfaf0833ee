package com.jsh.erp.datasource.entities;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存盘点明细表实体类
 * <AUTHOR>
 * @date 2024-12-23
 */
@TableName("jsh_inventory_check_detail")
public class InventoryCheckDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 盘点主表ID
     */
    private Long checkId;

    /**
     * 商品ID
     */
    private Long materialId;

    /**
     * 商品名称
     */
    private String materialName;

    /**
     * 商品规格
     */
    private String materialModel;

    /**
     * 商品单位
     */
    private String materialUnit;

    /**
     * 商品分类
     */
    private String materialCategory;

    /**
     * 账面数量
     */
    private BigDecimal bookNumber;

    /**
     * 实盘数量
     */
    private BigDecimal actualNumber;

    /**
     * 差异数量
     */
    private BigDecimal diffNumber;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 账面金额
     */
    private BigDecimal bookAmount;

    /**
     * 实盘金额
     */
    private BigDecimal actualAmount;

    /**
     * 差异金额
     */
    private BigDecimal diffAmount;

    /**
     * 差异率
     */
    private BigDecimal diffRate;

    /**
     * 批次号
     */
    private String batchNumber;

    /**
     * 有效期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date expirationDate;

    /**
     * 货位
     */
    private String position;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    private String deleteFlag;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialModel() {
        return materialModel;
    }

    public void setMaterialModel(String materialModel) {
        this.materialModel = materialModel;
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit;
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory;
    }

    public BigDecimal getBookNumber() {
        return bookNumber;
    }

    public void setBookNumber(BigDecimal bookNumber) {
        this.bookNumber = bookNumber;
    }

    public BigDecimal getActualNumber() {
        return actualNumber;
    }

    public void setActualNumber(BigDecimal actualNumber) {
        this.actualNumber = actualNumber;
    }

    public BigDecimal getDiffNumber() {
        return diffNumber;
    }

    public void setDiffNumber(BigDecimal diffNumber) {
        this.diffNumber = diffNumber;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getBookAmount() {
        return bookAmount;
    }

    public void setBookAmount(BigDecimal bookAmount) {
        this.bookAmount = bookAmount;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getDiffAmount() {
        return diffAmount;
    }

    public void setDiffAmount(BigDecimal diffAmount) {
        this.diffAmount = diffAmount;
    }

    public BigDecimal getDiffRate() {
        return diffRate;
    }

    public void setDiffRate(BigDecimal diffRate) {
        this.diffRate = diffRate;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public String toString() {
        return "InventoryCheckDetail{" +
                "id=" + id +
                ", checkId=" + checkId +
                ", materialId=" + materialId +
                ", materialName='" + materialName + '\'' +
                ", materialModel='" + materialModel + '\'' +
                ", materialUnit='" + materialUnit + '\'' +
                ", materialCategory='" + materialCategory + '\'' +
                ", bookNumber=" + bookNumber +
                ", actualNumber=" + actualNumber +
                ", diffNumber=" + diffNumber +
                ", unitPrice=" + unitPrice +
                ", bookAmount=" + bookAmount +
                ", actualAmount=" + actualAmount +
                ", diffAmount=" + diffAmount +
                ", diffRate=" + diffRate +
                ", batchNumber='" + batchNumber + '\'' +
                ", expirationDate=" + expirationDate +
                ", position='" + position + '\'' +
                ", remark='" + remark + '\'' +
                ", tenantId=" + tenantId +
                ", deleteFlag='" + deleteFlag + '\'' +
                '}';
    }
}
