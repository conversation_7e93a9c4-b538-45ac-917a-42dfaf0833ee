package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.InventoryCheck;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 库存盘点主表Mapper接口
 * <AUTHOR>
 * @date 2024-12-23
 */
public interface InventoryCheckMapper {

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(InventoryCheck record);

    /**
     * 选择性插入记录
     */
    int insertSelective(InventoryCheck record);

    /**
     * 根据主键查询
     */
    InventoryCheck selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     */
    int updateByPrimaryKeySelective(InventoryCheck record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(InventoryCheck record);

    /**
     * 根据条件查询盘点列表
     */
    List<InventoryCheck> selectByCondition(@Param("search") String search, 
                                          @Param("parameterMap") Map<String, Object> parameterMap);

    /**
     * 根据条件统计数量
     */
    int countByCondition(@Param("search") String search, 
                        @Param("parameterMap") Map<String, Object> parameterMap);

    /**
     * 获取盘点统计信息
     */
    Map<String, Object> getStatistics(@Param("search") String search, 
                                     @Param("parameterMap") Map<String, Object> parameterMap);

    /**
     * 根据盘点单号查询
     */
    InventoryCheck selectByNumber(@Param("number") String number, 
                                 @Param("tenantId") Long tenantId);

    /**
     * 批量删除
     */
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 根据仓库ID查询进行中的盘点
     */
    List<InventoryCheck> selectInProgressByDepotId(@Param("depotId") Long depotId, 
                                                  @Param("tenantId") Long tenantId);

    /**
     * 更新盘点统计数据
     */
    int updateStatistics(@Param("id") Long id,
                        @Param("totalCount") Integer totalCount,
                        @Param("diffCount") Integer diffCount,
                        @Param("totalAmount") java.math.BigDecimal totalAmount,
                        @Param("diffAmount") java.math.BigDecimal diffAmount,
                        @Param("accuracyRate") java.math.BigDecimal accuracyRate);

    /**
     * 根据状态查询盘点列表
     */
    List<InventoryCheck> selectByStatus(@Param("status") String status, 
                                       @Param("tenantId") Long tenantId);

    /**
     * 根据创建人查询盘点列表
     */
    List<InventoryCheck> selectByCreator(@Param("creator") Long creator, 
                                        @Param("tenantId") Long tenantId);

    /**
     * 根据时间范围查询盘点列表
     */
    List<InventoryCheck> selectByDateRange(@Param("startDate") String startDate,
                                          @Param("endDate") String endDate,
                                          @Param("tenantId") Long tenantId);

    /**
     * 获取月度盘点统计
     */
    List<Map<String, Object>> getMonthlyStatistics(@Param("year") Integer year,
                                                   @Param("tenantId") Long tenantId);

    /**
     * 获取仓库盘点统计
     */
    List<Map<String, Object>> getDepotStatistics(@Param("tenantId") Long tenantId);

    /**
     * 获取准确率趋势
     */
    List<Map<String, Object>> getAccuracyTrend(@Param("months") Integer months,
                                              @Param("tenantId") Long tenantId);
}
