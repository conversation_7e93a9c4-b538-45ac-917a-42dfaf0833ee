package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.constants.BusinessConstants;
import com.jsh.erp.datasource.entities.InventoryCheck;
import com.jsh.erp.datasource.entities.InventoryCheckDetail;
import com.jsh.erp.service.InventoryCheckService;
import com.jsh.erp.base.BaseController;
import com.jsh.erp.base.TableDataInfo;
import com.jsh.erp.utils.BaseResponseInfo;
import com.jsh.erp.utils.ErpInfo;
import com.jsh.erp.utils.PageUtils;
import com.jsh.erp.utils.StringUtil;
import com.jsh.erp.utils.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存盘点控制器
 * <AUTHOR>
 * @date 2024-12-23
 */
@RestController
@RequestMapping(value = "/inventoryCheck")
@Api(tags = {"库存盘点"})
public class InventoryCheckController extends BaseController {
    private Logger logger = LoggerFactory.getLogger(InventoryCheckController.class);

    @Resource
    private InventoryCheckService inventoryCheckService;

    /**
     * 查询盘点列表
     */
    @GetMapping(value = "/list")
    @ApiOperation(value = "查询盘点列表")
    public BaseResponseInfo getList(@RequestParam(value = "search", required = false) String search,
                                    @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                    @RequestParam(value = "currentPage", required = false) Integer currentPage,
                                    HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            Map<String, Object> parameterMap = new HashMap<>();
            // 从request中获取参数
            if (request.getParameter("depotId") != null) {
                parameterMap.put("depotId", Long.parseLong(request.getParameter("depotId")));
            }
            if (request.getParameter("status") != null) {
                parameterMap.put("status", request.getParameter("status"));
            }
            if (request.getParameter("startDate") != null) {
                parameterMap.put("startDate", request.getParameter("startDate"));
            }
            if (request.getParameter("endDate") != null) {
                parameterMap.put("endDate", request.getParameter("endDate"));
            }
            PageUtils.startPage(currentPage, pageSize);
            List<InventoryCheck> dataList = inventoryCheckService.select(search, parameterMap);
            res.code = 200;
            res.data = dataList;
        } catch (Exception e) {
            logger.error("查询盘点列表失败", e);
            res.code = 500;
            res.data = "查询失败";
        }
        return res;
    }

    /**
     * 创建盘点单
     */
    @PostMapping(value = "/add")
    @ApiOperation(value = "创建盘点单")
    public BaseResponseInfo addInventoryCheck(@RequestBody JSONObject obj,
                                              HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            inventoryCheckService.addInventoryCheck(obj, request);
            res.code = 200;
            res.data = "创建成功";
        } catch (Exception e) {
            logger.error("创建盘点单失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 更新盘点单
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "更新盘点单")
    public BaseResponseInfo updateInventoryCheck(@RequestBody JSONObject obj,
                                                 HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            inventoryCheckService.updateInventoryCheck(obj, request);
            res.code = 200;
            res.data = "更新成功";
        } catch (Exception e) {
            logger.error("更新盘点单失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 删除盘点单
     */
    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除盘点单")
    public BaseResponseInfo deleteInventoryCheck(@RequestParam("ids") String ids,
                                                 HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            inventoryCheckService.deleteInventoryCheck(ids, request);
            res.code = 200;
            res.data = "删除成功";
        } catch (Exception e) {
            logger.error("删除盘点单失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 获取盘点详情
     */
    @GetMapping(value = "/detail/{id}")
    @ApiOperation(value = "获取盘点详情")
    public BaseResponseInfo getDetail(@PathVariable Long id) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            InventoryCheck inventoryCheck = inventoryCheckService.getInventoryCheck(id);
            res.code = 200;
            res.data = inventoryCheck;
        } catch (Exception e) {
            logger.error("获取盘点详情失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 开始盘点
     */
    @PostMapping(value = "/start/{id}")
    @ApiOperation(value = "开始盘点")
    public BaseResponseInfo startInventoryCheck(@PathVariable Long id,
                                                HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            inventoryCheckService.startInventoryCheck(id, request);
            res.code = 200;
            res.data = "开始盘点成功";
        } catch (Exception e) {
            logger.error("开始盘点失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 完成盘点
     */
    @PostMapping(value = "/complete/{id}")
    @ApiOperation(value = "完成盘点")
    public BaseResponseInfo completeInventoryCheck(@PathVariable Long id,
                                                   HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            inventoryCheckService.completeInventoryCheck(id, request);
            res.code = 200;
            res.data = "完成盘点成功";
        } catch (Exception e) {
            logger.error("完成盘点失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 取消盘点
     */
    @PostMapping(value = "/cancel/{id}")
    @ApiOperation(value = "取消盘点")
    public BaseResponseInfo cancelInventoryCheck(@PathVariable Long id,
                                                 HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            inventoryCheckService.cancelInventoryCheck(id, request);
            res.code = 200;
            res.data = "取消盘点成功";
        } catch (Exception e) {
            logger.error("取消盘点失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 获取盘点明细
     */
    @GetMapping(value = "/details/{checkId}")
    @ApiOperation(value = "获取盘点明细")
    public BaseResponseInfo getCheckDetails(@PathVariable Long checkId) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            List<InventoryCheckDetail> details = inventoryCheckService.getCheckDetails(checkId);
            res.code = 200;
            res.data = details;
        } catch (Exception e) {
            logger.error("获取盘点明细失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 保存盘点明细
     */
    @PostMapping(value = "/saveDetails")
    @ApiOperation(value = "保存盘点明细")
    public BaseResponseInfo saveCheckDetails(@RequestBody JSONObject obj,
                                             HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            inventoryCheckService.saveCheckDetails(obj, request);
            res.code = 200;
            res.data = "保存成功";
        } catch (Exception e) {
            logger.error("保存盘点明细失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 获取盘点统计
     */
    @GetMapping(value = "/statistics")
    @ApiOperation(value = "获取盘点统计")
    public BaseResponseInfo getStatistics(@RequestParam(value = "search", required = false) String search,
                                          HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            Map<String, Object> parameterMap = new HashMap<>();
            Map<String, Object> statistics = inventoryCheckService.getStatistics(search, parameterMap);
            res.code = 200;
            res.data = statistics;
        } catch (Exception e) {
            logger.error("获取盘点统计失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 导出盘点数据
     */
    @GetMapping(value = "/export")
    @ApiOperation(value = "导出盘点数据")
    public BaseResponseInfo exportInventoryCheck(@RequestParam(value = "search", required = false) String search,
                                                 HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            Map<String, Object> parameterMap = new HashMap<>();
            String filePath = inventoryCheckService.exportInventoryCheck(search, parameterMap);
            res.code = 200;
            res.data = filePath;
        } catch (Exception e) {
            logger.error("导出盘点数据失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }
}
