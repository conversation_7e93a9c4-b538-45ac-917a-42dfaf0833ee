package com.jsh.erp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.constants.BusinessConstants;
import com.jsh.erp.datasource.entities.InventoryCheck;
import com.jsh.erp.datasource.entities.InventoryCheckDetail;
import com.jsh.erp.datasource.entities.User;
import com.jsh.erp.datasource.mappers.InventoryCheckMapper;
import com.jsh.erp.datasource.mappers.InventoryCheckDetailMapper;
import com.jsh.erp.service.user.UserService;
import com.jsh.erp.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存盘点服务类
 * <AUTHOR>
 * @date 2024-12-23
 */
@Service
public class InventoryCheckService {
    private Logger logger = LoggerFactory.getLogger(InventoryCheckService.class);

    @Resource
    private InventoryCheckMapper inventoryCheckMapper;

    @Resource
    private InventoryCheckDetailMapper inventoryCheckDetailMapper;

    @Resource
    private UserService userService;

    /**
     * 查询盘点列表
     */
    public List<InventoryCheck> select(String search, Map<String, Object> parameterMap) throws Exception {
        return inventoryCheckMapper.selectByCondition(search, parameterMap);
    }

    /**
     * 创建盘点单
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void addInventoryCheck(JSONObject obj, HttpServletRequest request) throws Exception {
        InventoryCheck inventoryCheck = new InventoryCheck();
        
        // 生成盘点单号
        String number = generateCheckNumber();
        inventoryCheck.setNumber(number);
        
        // 设置基本信息
        inventoryCheck.setDepotId(obj.getLong("depotId"));
        inventoryCheck.setDepotName(obj.getString("depotName"));
        inventoryCheck.setStatus("0"); // 草稿状态
        inventoryCheck.setRemark(obj.getString("remark"));
        
        // 设置创建人信息
        User user = userService.getCurrentUser();
        inventoryCheck.setCreator(user.getId());
        inventoryCheck.setCreatorName(user.getUsername());
        inventoryCheck.setCreateTime(new Date());
        
        // 设置租户信息
        inventoryCheck.setTenantId(user.getTenantId());
        inventoryCheck.setDeleteFlag("0");
        
        // 初始化统计数据
        inventoryCheck.setTotalCount(0);
        inventoryCheck.setDiffCount(0);
        inventoryCheck.setTotalAmount(BigDecimal.ZERO);
        inventoryCheck.setDiffAmount(BigDecimal.ZERO);
        inventoryCheck.setAccuracyRate(BigDecimal.ZERO);
        
        inventoryCheckMapper.insertSelective(inventoryCheck);
        
        // 如果有明细数据，一并保存
        if (obj.containsKey("details")) {
            JSONArray details = obj.getJSONArray("details");
            saveCheckDetailList(inventoryCheck.getId(), details);
        }
    }

    /**
     * 更新盘点单
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void updateInventoryCheck(JSONObject obj, HttpServletRequest request) throws Exception {
        Long id = obj.getLong("id");
        InventoryCheck inventoryCheck = inventoryCheckMapper.selectByPrimaryKey(id);
        
        if (inventoryCheck == null) {
            throw new Exception("盘点单不存在");
        }
        
        // 只有草稿状态才能修改
        if (!"0".equals(inventoryCheck.getStatus())) {
            throw new Exception("只有草稿状态的盘点单才能修改");
        }
        
        // 更新基本信息
        inventoryCheck.setDepotId(obj.getLong("depotId"));
        inventoryCheck.setDepotName(obj.getString("depotName"));
        inventoryCheck.setRemark(obj.getString("remark"));
        
        inventoryCheckMapper.updateByPrimaryKeySelective(inventoryCheck);
        
        // 更新明细数据
        if (obj.containsKey("details")) {
            // 先删除原有明细
            inventoryCheckDetailMapper.deleteByCheckId(id);
            // 保存新明细
            JSONArray details = obj.getJSONArray("details");
            saveCheckDetailList(id, details);
        }
    }

    /**
     * 删除盘点单
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void deleteInventoryCheck(String ids, HttpServletRequest request) throws Exception {
        String[] idArray = ids.split(",");
        for (String idStr : idArray) {
            Long id = Long.parseLong(idStr);
            InventoryCheck inventoryCheck = inventoryCheckMapper.selectByPrimaryKey(id);
            
            if (inventoryCheck == null) {
                continue;
            }
            
            // 只有草稿状态才能删除
            if (!"0".equals(inventoryCheck.getStatus())) {
                throw new Exception("只有草稿状态的盘点单才能删除");
            }
            
            // 逻辑删除
            inventoryCheck.setDeleteFlag("1");
            inventoryCheckMapper.updateByPrimaryKeySelective(inventoryCheck);
            
            // 删除明细
            inventoryCheckDetailMapper.deleteByCheckId(id);
        }
    }

    /**
     * 获取盘点详情
     */
    public InventoryCheck getInventoryCheck(Long id) throws Exception {
        return inventoryCheckMapper.selectByPrimaryKey(id);
    }

    /**
     * 开始盘点
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void startInventoryCheck(Long id, HttpServletRequest request) throws Exception {
        InventoryCheck inventoryCheck = inventoryCheckMapper.selectByPrimaryKey(id);
        
        if (inventoryCheck == null) {
            throw new Exception("盘点单不存在");
        }
        
        if (!"0".equals(inventoryCheck.getStatus())) {
            throw new Exception("只有草稿状态的盘点单才能开始盘点");
        }
        
        // 更新状态为盘点中
        inventoryCheck.setStatus("1");
        inventoryCheckMapper.updateByPrimaryKeySelective(inventoryCheck);
        
        // 生成盘点明细（从库存中获取当前库存数据）
        generateCheckDetails(id, inventoryCheck.getDepotId());
    }

    /**
     * 完成盘点
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void completeInventoryCheck(Long id, HttpServletRequest request) throws Exception {
        InventoryCheck inventoryCheck = inventoryCheckMapper.selectByPrimaryKey(id);
        
        if (inventoryCheck == null) {
            throw new Exception("盘点单不存在");
        }
        
        if (!"1".equals(inventoryCheck.getStatus())) {
            throw new Exception("只有盘点中状态的盘点单才能完成");
        }
        
        // 计算盘点统计数据
        calculateCheckStatistics(id);
        
        // 更新状态为已完成
        inventoryCheck.setStatus("2");
        inventoryCheck.setCompleteTime(new Date());
        inventoryCheckMapper.updateByPrimaryKeySelective(inventoryCheck);
    }

    /**
     * 取消盘点
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void cancelInventoryCheck(Long id, HttpServletRequest request) throws Exception {
        InventoryCheck inventoryCheck = inventoryCheckMapper.selectByPrimaryKey(id);
        
        if (inventoryCheck == null) {
            throw new Exception("盘点单不存在");
        }
        
        if ("2".equals(inventoryCheck.getStatus())) {
            throw new Exception("已完成的盘点单不能取消");
        }
        
        // 更新状态为已取消
        inventoryCheck.setStatus("3");
        inventoryCheckMapper.updateByPrimaryKeySelective(inventoryCheck);
    }

    /**
     * 获取盘点明细
     */
    public List<InventoryCheckDetail> getCheckDetails(Long checkId) throws Exception {
        return inventoryCheckDetailMapper.selectByCheckId(checkId);
    }

    /**
     * 保存盘点明细
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void saveCheckDetails(JSONObject obj, HttpServletRequest request) throws Exception {
        Long checkId = obj.getLong("checkId");
        JSONArray details = obj.getJSONArray("details");
        
        // 验证盘点单状态
        InventoryCheck inventoryCheck = inventoryCheckMapper.selectByPrimaryKey(checkId);
        if (inventoryCheck == null || !"1".equals(inventoryCheck.getStatus())) {
            throw new Exception("只有盘点中状态的盘点单才能录入数据");
        }
        
        // 保存明细数据
        for (int i = 0; i < details.size(); i++) {
            JSONObject detail = details.getJSONObject(i);
            InventoryCheckDetail checkDetail = new InventoryCheckDetail();
            
            checkDetail.setId(detail.getLong("id"));
            checkDetail.setActualNumber(detail.getBigDecimal("actualNumber"));
            checkDetail.setRemark(detail.getString("remark"));
            
            // 计算差异
            BigDecimal bookNumber = detail.getBigDecimal("bookNumber");
            BigDecimal actualNumber = detail.getBigDecimal("actualNumber");
            BigDecimal diffNumber = actualNumber.subtract(bookNumber);
            checkDetail.setDiffNumber(diffNumber);
            
            // 计算金额
            BigDecimal unitPrice = detail.getBigDecimal("unitPrice");
            checkDetail.setActualAmount(actualNumber.multiply(unitPrice));
            checkDetail.setDiffAmount(diffNumber.multiply(unitPrice));
            
            // 计算差异率
            if (bookNumber.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal diffRate = diffNumber.divide(bookNumber, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
                checkDetail.setDiffRate(diffRate);
            }
            
            inventoryCheckDetailMapper.updateByPrimaryKeySelective(checkDetail);
        }
    }

    /**
     * 获取盘点统计
     */
    public Map<String, Object> getStatistics(String search, Map<String, Object> parameterMap) throws Exception {
        return inventoryCheckMapper.getStatistics(search, parameterMap);
    }

    /**
     * 导出盘点数据
     */
    public String exportInventoryCheck(String search, Map<String, Object> parameterMap) throws Exception {
        // TODO: 实现导出功能
        return "export_file_path";
    }

    /**
     * 生成盘点单号
     */
    private String generateCheckNumber() {
        return "PD" + StringUtil.getDateTimeString();
    }

    /**
     * 保存盘点明细列表
     */
    private void saveCheckDetailList(Long checkId, JSONArray details) throws Exception {
        for (int i = 0; i < details.size(); i++) {
            JSONObject detail = details.getJSONObject(i);
            InventoryCheckDetail checkDetail = new InventoryCheckDetail();
            
            checkDetail.setCheckId(checkId);
            checkDetail.setMaterialId(detail.getLong("materialId"));
            checkDetail.setMaterialName(detail.getString("materialName"));
            checkDetail.setMaterialModel(detail.getString("materialModel"));
            checkDetail.setMaterialUnit(detail.getString("materialUnit"));
            checkDetail.setBookNumber(detail.getBigDecimal("bookNumber"));
            checkDetail.setUnitPrice(detail.getBigDecimal("unitPrice"));
            checkDetail.setBookAmount(detail.getBigDecimal("bookAmount"));
            checkDetail.setDeleteFlag("0");
            
            inventoryCheckDetailMapper.insertSelective(checkDetail);
        }
    }

    /**
     * 生成盘点明细
     */
    private void generateCheckDetails(Long checkId, Long depotId) throws Exception {
        // TODO: 从库存表中获取当前库存数据，生成盘点明细
        // 这里需要查询 jsh_material_current_stock 表
    }

    /**
     * 计算盘点统计数据
     */
    private void calculateCheckStatistics(Long checkId) throws Exception {
        List<InventoryCheckDetail> details = inventoryCheckDetailMapper.selectByCheckId(checkId);
        
        int totalCount = details.size();
        int diffCount = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal diffAmount = BigDecimal.ZERO;
        
        for (InventoryCheckDetail detail : details) {
            if (detail.getDiffNumber() != null && detail.getDiffNumber().compareTo(BigDecimal.ZERO) != 0) {
                diffCount++;
            }
            if (detail.getActualAmount() != null) {
                totalAmount = totalAmount.add(detail.getActualAmount());
            }
            if (detail.getDiffAmount() != null) {
                diffAmount = diffAmount.add(detail.getDiffAmount());
            }
        }
        
        // 计算准确率
        BigDecimal accuracyRate = BigDecimal.ZERO;
        if (totalCount > 0) {
            accuracyRate = new BigDecimal(totalCount - diffCount)
                    .divide(new BigDecimal(totalCount), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
        }
        
        // 更新主表统计数据
        InventoryCheck inventoryCheck = inventoryCheckMapper.selectByPrimaryKey(checkId);
        inventoryCheck.setTotalCount(totalCount);
        inventoryCheck.setDiffCount(diffCount);
        inventoryCheck.setTotalAmount(totalAmount);
        inventoryCheck.setDiffAmount(diffAmount);
        inventoryCheck.setAccuracyRate(accuracyRate);
        
        inventoryCheckMapper.updateByPrimaryKeySelective(inventoryCheck);
    }
}
