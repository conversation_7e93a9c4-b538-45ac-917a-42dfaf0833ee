<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.InventoryCheckMapper">
  
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.InventoryCheck">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="depot_id" jdbcType="BIGINT" property="depotId" />
    <result column="depot_name" jdbcType="VARCHAR" property="depotName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="diff_count" jdbcType="INTEGER" property="diffCount" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="diff_amount" jdbcType="DECIMAL" property="diffAmount" />
    <result column="accuracy_rate" jdbcType="DECIMAL" property="accuracyRate" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="auditor" jdbcType="BIGINT" property="auditor" />
    <result column="auditor_name" jdbcType="VARCHAR" property="auditorName" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>

  <sql id="Base_Column_List">
    id, number, depot_id, depot_name, status, total_count, diff_count, total_amount, 
    diff_amount, accuracy_rate, creator, creator_name, create_time, auditor, auditor_name, 
    audit_time, complete_time, remark, tenant_id, delete_flag
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_inventory_check
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_inventory_check
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.InventoryCheck">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_inventory_check (number, depot_id, depot_name, 
      status, total_count, diff_count, 
      total_amount, diff_amount, accuracy_rate, 
      creator, creator_name, create_time, 
      auditor, auditor_name, audit_time, 
      complete_time, remark, tenant_id, 
      delete_flag)
    values (#{number,jdbcType=VARCHAR}, #{depotId,jdbcType=BIGINT}, #{depotName,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{totalCount,jdbcType=INTEGER}, #{diffCount,jdbcType=INTEGER}, 
      #{totalAmount,jdbcType=DECIMAL}, #{diffAmount,jdbcType=DECIMAL}, #{accuracyRate,jdbcType=DECIMAL}, 
      #{creator,jdbcType=BIGINT}, #{creatorName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{auditor,jdbcType=BIGINT}, #{auditorName,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{completeTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, 
      #{deleteFlag,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.InventoryCheck">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_inventory_check
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="number != null">
        number,
      </if>
      <if test="depotId != null">
        depot_id,
      </if>
      <if test="depotName != null">
        depot_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="totalCount != null">
        total_count,
      </if>
      <if test="diffCount != null">
        diff_count,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="diffAmount != null">
        diff_amount,
      </if>
      <if test="accuracyRate != null">
        accuracy_rate,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="auditorName != null">
        auditor_name,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="depotId != null">
        #{depotId,jdbcType=BIGINT},
      </if>
      <if test="depotName != null">
        #{depotName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="totalCount != null">
        #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="diffCount != null">
        #{diffCount,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffAmount != null">
        #{diffAmount,jdbcType=DECIMAL},
      </if>
      <if test="accuracyRate != null">
        #{accuracyRate,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=BIGINT},
      </if>
      <if test="auditorName != null">
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.InventoryCheck">
    update jsh_inventory_check
    <set>
      <if test="number != null">
        number = #{number,jdbcType=VARCHAR},
      </if>
      <if test="depotId != null">
        depot_id = #{depotId,jdbcType=BIGINT},
      </if>
      <if test="depotName != null">
        depot_name = #{depotName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="totalCount != null">
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="diffCount != null">
        diff_count = #{diffCount,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffAmount != null">
        diff_amount = #{diffAmount,jdbcType=DECIMAL},
      </if>
      <if test="accuracyRate != null">
        accuracy_rate = #{accuracyRate,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=BIGINT},
      </if>
      <if test="auditorName != null">
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.InventoryCheck">
    update jsh_inventory_check
    set number = #{number,jdbcType=VARCHAR},
      depot_id = #{depotId,jdbcType=BIGINT},
      depot_name = #{depotName,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      total_count = #{totalCount,jdbcType=INTEGER},
      diff_count = #{diffCount,jdbcType=INTEGER},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      diff_amount = #{diffAmount,jdbcType=DECIMAL},
      accuracy_rate = #{accuracyRate,jdbcType=DECIMAL},
      creator = #{creator,jdbcType=BIGINT},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      auditor = #{auditor,jdbcType=BIGINT},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据条件查询盘点列表 -->
  <select id="selectByCondition" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_inventory_check
    <where>
      delete_flag = '0'
      <if test="parameterMap.tenantId != null">
        and tenant_id = #{parameterMap.tenantId}
      </if>
      <if test="search != null and search != ''">
        and (number like concat('%', #{search}, '%') 
             or depot_name like concat('%', #{search}, '%')
             or creator_name like concat('%', #{search}, '%'))
      </if>
      <if test="parameterMap.status != null and parameterMap.status != ''">
        and status = #{parameterMap.status}
      </if>
      <if test="parameterMap.depotId != null">
        and depot_id = #{parameterMap.depotId}
      </if>
      <if test="parameterMap.startDate != null and parameterMap.startDate != ''">
        and create_time >= #{parameterMap.startDate}
      </if>
      <if test="parameterMap.endDate != null and parameterMap.endDate != ''">
        and create_time &lt;= #{parameterMap.endDate}
      </if>
    </where>
    order by create_time desc
  </select>

  <!-- 根据条件统计数量 -->
  <select id="countByCondition" resultType="int">
    select count(1)
    from jsh_inventory_check
    <where>
      delete_flag = '0'
      <if test="parameterMap.tenantId != null">
        and tenant_id = #{parameterMap.tenantId}
      </if>
      <if test="search != null and search != ''">
        and (number like concat('%', #{search}, '%') 
             or depot_name like concat('%', #{search}, '%')
             or creator_name like concat('%', #{search}, '%'))
      </if>
      <if test="parameterMap.status != null and parameterMap.status != ''">
        and status = #{parameterMap.status}
      </if>
      <if test="parameterMap.depotId != null">
        and depot_id = #{parameterMap.depotId}
      </if>
      <if test="parameterMap.startDate != null and parameterMap.startDate != ''">
        and create_time >= #{parameterMap.startDate}
      </if>
      <if test="parameterMap.endDate != null and parameterMap.endDate != ''">
        and create_time &lt;= #{parameterMap.endDate}
      </if>
    </where>
  </select>

  <!-- 获取盘点统计信息 -->
  <select id="getStatistics" resultType="map">
    select 
      count(1) as totalChecks,
      sum(case when status = '1' then 1 else 0 end) as inProgressChecks,
      sum(case when status = '2' then 1 else 0 end) as completedChecks,
      avg(case when status = '2' and accuracy_rate is not null then accuracy_rate else null end) as avgAccuracy
    from jsh_inventory_check
    <where>
      delete_flag = '0'
      <if test="parameterMap.tenantId != null">
        and tenant_id = #{parameterMap.tenantId}
      </if>
    </where>
  </select>

</mapper>
