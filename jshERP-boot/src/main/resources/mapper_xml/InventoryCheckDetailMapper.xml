<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.InventoryCheckDetailMapper">
  
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.InventoryCheckDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="check_id" jdbcType="BIGINT" property="checkId" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_model" jdbcType="VARCHAR" property="materialModel" />
    <result column="material_unit" jdbcType="VARCHAR" property="materialUnit" />
    <result column="material_category" jdbcType="VARCHAR" property="materialCategory" />
    <result column="book_number" jdbcType="DECIMAL" property="bookNumber" />
    <result column="actual_number" jdbcType="DECIMAL" property="actualNumber" />
    <result column="diff_number" jdbcType="DECIMAL" property="diffNumber" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="book_amount" jdbcType="DECIMAL" property="bookAmount" />
    <result column="actual_amount" jdbcType="DECIMAL" property="actualAmount" />
    <result column="diff_amount" jdbcType="DECIMAL" property="diffAmount" />
    <result column="diff_rate" jdbcType="DECIMAL" property="diffRate" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
    <result column="expiration_date" jdbcType="DATE" property="expirationDate" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>

  <sql id="Base_Column_List">
    id, check_id, material_id, material_name, material_model, material_unit, material_category,
    book_number, actual_number, diff_number, unit_price, book_amount, actual_amount, 
    diff_amount, diff_rate, batch_number, expiration_date, position, remark, tenant_id, delete_flag
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_inventory_check_detail
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_inventory_check_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.InventoryCheckDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_inventory_check_detail (check_id, material_id, material_name, 
      material_model, material_unit, material_category, 
      book_number, actual_number, diff_number, 
      unit_price, book_amount, actual_amount, 
      diff_amount, diff_rate, batch_number, 
      expiration_date, position, remark, 
      tenant_id, delete_flag)
    values (#{checkId,jdbcType=BIGINT}, #{materialId,jdbcType=BIGINT}, #{materialName,jdbcType=VARCHAR}, 
      #{materialModel,jdbcType=VARCHAR}, #{materialUnit,jdbcType=VARCHAR}, #{materialCategory,jdbcType=VARCHAR}, 
      #{bookNumber,jdbcType=DECIMAL}, #{actualNumber,jdbcType=DECIMAL}, #{diffNumber,jdbcType=DECIMAL}, 
      #{unitPrice,jdbcType=DECIMAL}, #{bookAmount,jdbcType=DECIMAL}, #{actualAmount,jdbcType=DECIMAL}, 
      #{diffAmount,jdbcType=DECIMAL}, #{diffRate,jdbcType=DECIMAL}, #{batchNumber,jdbcType=VARCHAR}, 
      #{expirationDate,jdbcType=DATE}, #{position,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.InventoryCheckDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_inventory_check_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkId != null">
        check_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="materialModel != null">
        material_model,
      </if>
      <if test="materialUnit != null">
        material_unit,
      </if>
      <if test="materialCategory != null">
        material_category,
      </if>
      <if test="bookNumber != null">
        book_number,
      </if>
      <if test="actualNumber != null">
        actual_number,
      </if>
      <if test="diffNumber != null">
        diff_number,
      </if>
      <if test="unitPrice != null">
        unit_price,
      </if>
      <if test="bookAmount != null">
        book_amount,
      </if>
      <if test="actualAmount != null">
        actual_amount,
      </if>
      <if test="diffAmount != null">
        diff_amount,
      </if>
      <if test="diffRate != null">
        diff_rate,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="expirationDate != null">
        expiration_date,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="checkId != null">
        #{checkId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialModel != null">
        #{materialModel,jdbcType=VARCHAR},
      </if>
      <if test="materialUnit != null">
        #{materialUnit,jdbcType=VARCHAR},
      </if>
      <if test="materialCategory != null">
        #{materialCategory,jdbcType=VARCHAR},
      </if>
      <if test="bookNumber != null">
        #{bookNumber,jdbcType=DECIMAL},
      </if>
      <if test="actualNumber != null">
        #{actualNumber,jdbcType=DECIMAL},
      </if>
      <if test="diffNumber != null">
        #{diffNumber,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="bookAmount != null">
        #{bookAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualAmount != null">
        #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffAmount != null">
        #{diffAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffRate != null">
        #{diffRate,jdbcType=DECIMAL},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=DATE},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.InventoryCheckDetail">
    update jsh_inventory_check_detail
    <set>
      <if test="checkId != null">
        check_id = #{checkId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="materialName != null">
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialModel != null">
        material_model = #{materialModel,jdbcType=VARCHAR},
      </if>
      <if test="materialUnit != null">
        material_unit = #{materialUnit,jdbcType=VARCHAR},
      </if>
      <if test="materialCategory != null">
        material_category = #{materialCategory,jdbcType=VARCHAR},
      </if>
      <if test="bookNumber != null">
        book_number = #{bookNumber,jdbcType=DECIMAL},
      </if>
      <if test="actualNumber != null">
        actual_number = #{actualNumber,jdbcType=DECIMAL},
      </if>
      <if test="diffNumber != null">
        diff_number = #{diffNumber,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        unit_price = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="bookAmount != null">
        book_amount = #{bookAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualAmount != null">
        actual_amount = #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffAmount != null">
        diff_amount = #{diffAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffRate != null">
        diff_rate = #{diffRate,jdbcType=DECIMAL},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        expiration_date = #{expirationDate,jdbcType=DATE},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.InventoryCheckDetail">
    update jsh_inventory_check_detail
    set check_id = #{checkId,jdbcType=BIGINT},
      material_id = #{materialId,jdbcType=BIGINT},
      material_name = #{materialName,jdbcType=VARCHAR},
      material_model = #{materialModel,jdbcType=VARCHAR},
      material_unit = #{materialUnit,jdbcType=VARCHAR},
      material_category = #{materialCategory,jdbcType=VARCHAR},
      book_number = #{bookNumber,jdbcType=DECIMAL},
      actual_number = #{actualNumber,jdbcType=DECIMAL},
      diff_number = #{diffNumber,jdbcType=DECIMAL},
      unit_price = #{unitPrice,jdbcType=DECIMAL},
      book_amount = #{bookAmount,jdbcType=DECIMAL},
      actual_amount = #{actualAmount,jdbcType=DECIMAL},
      diff_amount = #{diffAmount,jdbcType=DECIMAL},
      diff_rate = #{diffRate,jdbcType=DECIMAL},
      batch_number = #{batchNumber,jdbcType=VARCHAR},
      expiration_date = #{expirationDate,jdbcType=DATE},
      position = #{position,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据盘点ID查询明细列表 -->
  <select id="selectByCheckId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_inventory_check_detail
    where check_id = #{checkId,jdbcType=BIGINT}
      and delete_flag = '0'
    order by material_name, material_model
  </select>

  <!-- 根据盘点ID删除明细 -->
  <update id="deleteByCheckId" parameterType="java.lang.Long">
    update jsh_inventory_check_detail
    set delete_flag = '1'
    where check_id = #{checkId,jdbcType=BIGINT}
  </update>

  <!-- 批量插入明细 -->
  <insert id="batchInsert" parameterType="java.util.List">
    insert into jsh_inventory_check_detail (check_id, material_id, material_name, 
      material_model, material_unit, material_category, book_number, actual_number, 
      diff_number, unit_price, book_amount, actual_amount, diff_amount, diff_rate, 
      batch_number, expiration_date, position, remark, tenant_id, delete_flag)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.checkId}, #{item.materialId}, #{item.materialName}, #{item.materialModel}, 
       #{item.materialUnit}, #{item.materialCategory}, #{item.bookNumber}, #{item.actualNumber}, 
       #{item.diffNumber}, #{item.unitPrice}, #{item.bookAmount}, #{item.actualAmount}, 
       #{item.diffAmount}, #{item.diffRate}, #{item.batchNumber}, #{item.expirationDate}, 
       #{item.position}, #{item.remark}, #{item.tenantId}, #{item.deleteFlag})
    </foreach>
  </insert>

  <!-- 根据盘点ID统计明细数量 -->
  <select id="countByCheckId" parameterType="java.lang.Long" resultType="int">
    select count(1)
    from jsh_inventory_check_detail
    where check_id = #{checkId,jdbcType=BIGINT}
      and delete_flag = '0'
  </select>

  <!-- 根据盘点ID统计差异数量 -->
  <select id="countDiffByCheckId" parameterType="java.lang.Long" resultType="int">
    select count(1)
    from jsh_inventory_check_detail
    where check_id = #{checkId,jdbcType=BIGINT}
      and delete_flag = '0'
      and diff_number != 0
  </select>

  <!-- 根据盘点ID计算总金额 -->
  <select id="sumTotalAmountByCheckId" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
    select coalesce(sum(actual_amount), 0)
    from jsh_inventory_check_detail
    where check_id = #{checkId,jdbcType=BIGINT}
      and delete_flag = '0'
  </select>

  <!-- 根据盘点ID计算差异金额 -->
  <select id="sumDiffAmountByCheckId" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
    select coalesce(sum(diff_amount), 0)
    from jsh_inventory_check_detail
    where check_id = #{checkId,jdbcType=BIGINT}
      and delete_flag = '0'
  </select>

  <!-- 根据盘点ID查询有差异的明细 -->
  <select id="selectDiffByCheckId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_inventory_check_detail
    where check_id = #{checkId,jdbcType=BIGINT}
      and delete_flag = '0'
      and diff_number != 0
    order by abs(diff_amount) desc
  </select>

</mapper>
