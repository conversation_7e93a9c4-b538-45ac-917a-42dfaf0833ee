import { BasicLayout } from '@/layouts'

/**
 * 库存盘点模块路由配置
 */
const inventoryCheckRouter = {
  path: '/inventory',
  name: 'inventory',
  component: BasicLayout,
  meta: {
    title: '库存管理',
    icon: 'database',
    permission: ['inventory']
  },
  redirect: '/inventory/check/list',
  children: [
    {
      path: '/inventory/check',
      name: 'inventoryCheck',
      component: () => import('@/layouts/RouteView'),
      meta: {
        title: '库存盘点',
        icon: 'audit',
        permission: ['inventory:check']
      },
      redirect: '/inventory/check/list',
      children: [
        {
          path: '/inventory/check/list',
          name: 'InventoryCheckList',
          component: () => import('@/views/inventory/InventoryCheckList'),
          meta: {
            title: '盘点列表',
            icon: 'table',
            permission: ['inventory:check:list']
          }
        },
        {
          path: '/inventory/check/add',
          name: 'InventoryCheckAdd',
          component: () => import('@/views/inventory/InventoryCheckForm'),
          meta: {
            title: '新增盘点',
            icon: 'plus',
            permission: ['inventory:check:add'],
            hidden: true
          }
        },
        {
          path: '/inventory/check/edit/:id',
          name: 'InventoryCheckEdit',
          component: () => import('@/views/inventory/InventoryCheckForm'),
          meta: {
            title: '编辑盘点',
            icon: 'edit',
            permission: ['inventory:check:edit'],
            hidden: true
          }
        },
        {
          path: '/inventory/check/detail/:id',
          name: 'InventoryCheckDetail',
          component: () => import('@/views/inventory/InventoryCheckDetail'),
          meta: {
            title: '盘点详情',
            icon: 'eye',
            permission: ['inventory:check:view'],
            hidden: true
          }
        },
        {
          path: '/inventory/check/input/:id',
          name: 'InventoryCheckInput',
          component: () => import('@/views/inventory/InventoryCheckInput'),
          meta: {
            title: '盘点录入',
            icon: 'form',
            permission: ['inventory:check:input'],
            hidden: true
          }
        }
      ]
    },
    {
      path: '/inventory/statistics',
      name: 'inventoryStatistics',
      component: () => import('@/views/inventory/InventoryStatistics'),
      meta: {
        title: '盘点统计',
        icon: 'bar-chart',
        permission: ['inventory:statistics']
      }
    },
    {
      path: '/inventory/settings',
      name: 'inventorySettings',
      component: () => import('@/views/inventory/InventorySettings'),
      meta: {
        title: '盘点设置',
        icon: 'setting',
        permission: ['inventory:settings']
      }
    }
  ]
}

export default inventoryCheckRouter
