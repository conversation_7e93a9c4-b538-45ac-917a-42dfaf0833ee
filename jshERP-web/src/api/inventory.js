import { axios } from '@/utils/request'

const api = {
  // 盘点管理
  inventoryCheck: '/inventoryCheck',
  inventoryCheckList: '/inventoryCheck/list',
  inventoryCheckAdd: '/inventoryCheck/add',
  inventoryCheckUpdate: '/inventoryCheck/update',
  inventoryCheckDelete: '/inventoryCheck/delete',
  inventoryCheckDetail: '/inventoryCheck/detail',
  inventoryCheckStart: '/inventoryCheck/start',
  inventoryCheckComplete: '/inventoryCheck/complete',
  inventoryCheckCancel: '/inventoryCheck/cancel',
  inventoryCheckDetails: '/inventoryCheck/details',
  inventoryCheckSaveDetails: '/inventoryCheck/saveDetails',
  inventoryCheckStatistics: '/inventoryCheck/statistics',
  inventoryCheckExport: '/inventoryCheck/export',
  
  // 仓库管理
  depotList: '/depot/list',
  
  // 商品管理
  materialList: '/material/list',
  materialListByIds: '/material/listByIds',
  materialCategory: '/materialCategory/list',
  materialCategoryTree: '/materialCategory/tree',
  
  // 供应商管理
  supplierList: '/supplier/list'
}

/**
 * 库存盘点API
 */
export default api

/**
 * 获取盘点列表
 * @param {Object} parameter 查询参数
 */
export function getInventoryCheckList(parameter) {
  return axios({
    url: api.inventoryCheckList,
    method: 'get',
    params: parameter
  })
}

/**
 * 创建盘点单
 * @param {Object} data 盘点数据
 */
export function addInventoryCheck(data) {
  return axios({
    url: api.inventoryCheckAdd,
    method: 'post',
    data: data
  })
}

/**
 * 更新盘点单
 * @param {Object} data 盘点数据
 */
export function updateInventoryCheck(data) {
  return axios({
    url: api.inventoryCheckUpdate,
    method: 'put',
    data: data
  })
}

/**
 * 删除盘点单
 * @param {String} ids 盘点ID，多个用逗号分隔
 */
export function deleteInventoryCheck(ids) {
  return axios({
    url: api.inventoryCheckDelete,
    method: 'delete',
    params: { ids }
  })
}

/**
 * 获取盘点详情
 * @param {Number} id 盘点ID
 */
export function getInventoryCheckDetail(id) {
  return axios({
    url: `${api.inventoryCheckDetail}/${id}`,
    method: 'get'
  })
}

/**
 * 开始盘点
 * @param {Number} id 盘点ID
 */
export function startInventoryCheck(id) {
  return axios({
    url: `${api.inventoryCheckStart}/${id}`,
    method: 'post'
  })
}

/**
 * 完成盘点
 * @param {Number} id 盘点ID
 */
export function completeInventoryCheck(id) {
  return axios({
    url: `${api.inventoryCheckComplete}/${id}`,
    method: 'post'
  })
}

/**
 * 取消盘点
 * @param {Number} id 盘点ID
 */
export function cancelInventoryCheck(id) {
  return axios({
    url: `${api.inventoryCheckCancel}/${id}`,
    method: 'post'
  })
}

/**
 * 获取盘点明细
 * @param {Number} checkId 盘点ID
 */
export function getInventoryCheckDetails(checkId) {
  return axios({
    url: `${api.inventoryCheckDetails}/${checkId}`,
    method: 'get'
  })
}

/**
 * 保存盘点明细
 * @param {Object} data 明细数据
 */
export function saveInventoryCheckDetails(data) {
  return axios({
    url: api.inventoryCheckSaveDetails,
    method: 'post',
    data: data
  })
}

/**
 * 获取盘点统计
 * @param {Object} parameter 查询参数
 */
export function getInventoryCheckStatistics(parameter) {
  return axios({
    url: api.inventoryCheckStatistics,
    method: 'get',
    params: parameter
  })
}

/**
 * 导出盘点数据
 * @param {Object} parameter 查询参数
 */
export function exportInventoryCheck(parameter) {
  return axios({
    url: api.inventoryCheckExport,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取仓库列表
 * @param {Object} parameter 查询参数
 */
export function getDepotList(parameter) {
  return axios({
    url: api.depotList,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取商品列表
 * @param {Object} parameter 查询参数
 */
export function getMaterialList(parameter) {
  return axios({
    url: api.materialList,
    method: 'get',
    params: parameter
  })
}

/**
 * 根据ID列表获取商品
 * @param {String} ids 商品ID，多个用逗号分隔
 */
export function getMaterialListByIds(ids) {
  return axios({
    url: api.materialListByIds,
    method: 'get',
    params: { ids }
  })
}

/**
 * 获取商品分类列表
 * @param {Object} parameter 查询参数
 */
export function getMaterialCategoryList(parameter) {
  return axios({
    url: api.materialCategory,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取商品分类树
 * @param {Object} parameter 查询参数
 */
export function getMaterialCategoryTree(parameter) {
  return axios({
    url: api.materialCategoryTree,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取供应商列表
 * @param {Object} parameter 查询参数
 */
export function getSupplierList(parameter) {
  return axios({
    url: api.supplierList,
    method: 'get',
    params: parameter
  })
}
