<template>
  <div class="inventory-check-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-header-content">
        <div class="page-title">
          <h2>📊 盘点详情</h2>
          <p>查看盘点结果和差异分析</p>
        </div>
        <div class="page-actions">
          <a-button @click="handleBack">
            <a-icon type="arrow-left" />
            返回列表
          </a-button>
          <a-button type="primary" @click="handleExport" :loading="exportLoading">
            <a-icon type="download" />
            导出报告
          </a-button>
        </div>
      </div>
    </div>

    <!-- 盘点概览 -->
    <div class="overview-container">
      <a-row :gutter="24">
        <!-- 基本信息 -->
        <a-col :span="16">
          <a-card title="基本信息" :bordered="false" class="info-card">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">盘点单号</span>
                <span class="value">{{ checkInfo.number }}</span>
              </div>
              <div class="info-item">
                <span class="label">仓库</span>
                <span class="value">{{ checkInfo.depotName }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态</span>
                <a-tag :color="getStatusColor(checkInfo.status)" class="status-tag">
                  <a-icon :type="getStatusIcon(checkInfo.status)" />
                  {{ getStatusText(checkInfo.status) }}
                </a-tag>
              </div>
              <div class="info-item">
                <span class="label">创建人</span>
                <span class="value">{{ checkInfo.creatorName }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间</span>
                <span class="value">{{ checkInfo.createTime | moment }}</span>
              </div>
              <div class="info-item">
                <span class="label">完成时间</span>
                <span class="value">{{ checkInfo.completeTime | moment }}</span>
              </div>
              <div class="info-item" v-if="checkInfo.remark">
                <span class="label">备注</span>
                <span class="value">{{ checkInfo.remark }}</span>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 统计信息 -->
        <a-col :span="8">
          <a-card title="统计信息" :bordered="false" class="stats-card">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ checkInfo.totalCount || 0 }}</div>
                <div class="stat-label">盘点商品数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value diff-count">{{ checkInfo.diffCount || 0 }}</div>
                <div class="stat-label">差异商品数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value accuracy-rate" :class="getAccuracyClass(checkInfo.accuracyRate)">
                  {{ checkInfo.accuracyRate || 0 }}%
                </div>
                <div class="stat-label">准确率</div>
              </div>
              <div class="stat-item">
                <div class="stat-value total-amount">{{ formatMoney(checkInfo.totalAmount) }}</div>
                <div class="stat-label">盘点总金额</div>
              </div>
              <div class="stat-item">
                <div class="stat-value diff-amount" :class="getDiffClass(checkInfo.diffAmount)">
                  {{ formatMoney(checkInfo.diffAmount) }}
                </div>
                <div class="stat-label">差异金额</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 差异分析图表 -->
    <div class="chart-container">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-card title="差异分布" :bordered="false" class="chart-card">
            <div ref="diffChart" class="chart" style="height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="金额分析" :bordered="false" class="chart-card">
            <div ref="amountChart" class="chart" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 盘点明细 -->
    <div class="detail-container">
      <a-card title="盘点明细" :bordered="false" class="detail-card">
        <!-- 筛选工具栏 -->
        <div class="filter-toolbar">
          <a-input-search
            v-model="searchKeyword"
            placeholder="搜索商品名称、规格"
            style="width: 300px;"
            @search="handleFilter"
            @change="handleFilter"
          />
          <a-select
            v-model="filterDiffType"
            placeholder="差异类型"
            style="width: 120px; margin-left: 12px;"
            allowClear
            @change="handleFilter"
          >
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="normal">无差异</a-select-option>
            <a-select-option value="positive">盘盈</a-select-option>
            <a-select-option value="negative">盘亏</a-select-option>
          </a-select>
          <a-select
            v-model="filterCategory"
            placeholder="商品分类"
            style="width: 150px; margin-left: 12px;"
            allowClear
            @change="handleFilter"
          >
            <a-select-option v-for="category in categoryList" :key="category.id" :value="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </div>

        <!-- 明细表格 -->
        <a-table
          ref="table"
          rowKey="id"
          :columns="columns"
          :dataSource="filteredDataSource"
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1400 }"
          size="middle"
          class="detail-table"
        >
          <template slot="materialInfo" slot-scope="text, record">
            <div class="material-info">
              <div class="material-name">{{ record.materialName }}</div>
              <div class="material-model">{{ record.materialModel }}</div>
            </div>
          </template>

          <template slot="bookNumber" slot-scope="text">
            <span class="number-text">{{ formatNumber(text) }}</span>
          </template>

          <template slot="actualNumber" slot-scope="text">
            <span class="number-text">{{ formatNumber(text) }}</span>
          </template>

          <template slot="diffNumber" slot-scope="text, record">
            <span class="diff-number" :class="getDiffClass(text)">
              {{ formatDiffNumber(text) }}
            </span>
          </template>

          <template slot="diffAmount" slot-scope="text">
            <span class="diff-amount" :class="getDiffClass(text)">
              {{ formatMoney(text) }}
            </span>
          </template>

          <template slot="diffRate" slot-scope="text">
            <span class="diff-rate" :class="getDiffClass(text)">
              {{ formatPercent(text) }}
            </span>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import * as echarts from 'echarts'

export default {
  name: 'InventoryCheckDetail',
  data() {
    return {
      // 盘点信息
      checkInfo: {},
      // 明细数据
      dataSource: [],
      // 过滤后的数据
      filteredDataSource: [],
      // 分类列表
      categoryList: [],
      // 加载状态
      loading: false,
      // 导出加载状态
      exportLoading: false,
      // 搜索关键词
      searchKeyword: '',
      // 筛选差异类型
      filterDiffType: 'all',
      // 筛选分类
      filterCategory: null,
      // 图表实例
      diffChart: null,
      amountChart: null,
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 20,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      },
      // 表格列配置
      columns: [
        {
          title: '商品信息',
          dataIndex: 'materialInfo',
          width: 200,
          fixed: 'left',
          scopedSlots: { customRender: 'materialInfo' }
        },
        {
          title: '分类',
          dataIndex: 'materialCategory',
          width: 120
        },
        {
          title: '单位',
          dataIndex: 'materialUnit',
          width: 80,
          align: 'center'
        },
        {
          title: '账面数量',
          dataIndex: 'bookNumber',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'bookNumber' }
        },
        {
          title: '实盘数量',
          dataIndex: 'actualNumber',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'actualNumber' }
        },
        {
          title: '差异数量',
          dataIndex: 'diffNumber',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'diffNumber' }
        },
        {
          title: '单价',
          dataIndex: 'unitPrice',
          width: 100,
          align: 'right',
          customRender: (text) => this.formatMoney(text)
        },
        {
          title: '账面金额',
          dataIndex: 'bookAmount',
          width: 120,
          align: 'right',
          customRender: (text) => this.formatMoney(text)
        },
        {
          title: '实盘金额',
          dataIndex: 'actualAmount',
          width: 120,
          align: 'right',
          customRender: (text) => this.formatMoney(text)
        },
        {
          title: '差异金额',
          dataIndex: 'diffAmount',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'diffAmount' }
        },
        {
          title: '差异率',
          dataIndex: 'diffRate',
          width: 100,
          align: 'right',
          scopedSlots: { customRender: 'diffRate' }
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: 150,
          ellipsis: true
        }
      ]
    }
  },

  created() {
    this.initPage()
  },

  mounted() {
    this.initCharts()
  },

  beforeDestroy() {
    if (this.diffChart) {
      this.diffChart.dispose()
    }
    if (this.amountChart) {
      this.amountChart.dispose()
    }
  },

  methods: {
    // 初始化页面
    initPage() {
      const { id } = this.$route.params
      this.checkId = id
      this.loadCheckInfo()
      this.loadCheckDetails()
      this.loadCategoryList()
    },

    // 加载盘点信息
    loadCheckInfo() {
      getAction(`/inventoryCheck/detail/${this.checkId}`).then(res => {
        if (res.success) {
          this.checkInfo = res.result
          this.$nextTick(() => {
            this.updateCharts()
          })
        }
      }).catch(err => {
        console.error('加载盘点信息失败:', err)
        this.$message.error('加载盘点信息失败')
      })
    },

    // 加载盘点明细
    loadCheckDetails() {
      this.loading = true
      getAction(`/inventoryCheck/details/${this.checkId}`).then(res => {
        if (res.success) {
          this.dataSource = res.result || []
          this.handleFilter()
          this.$nextTick(() => {
            this.updateCharts()
          })
        }
        this.loading = false
      }).catch(err => {
        console.error('加载盘点明细失败:', err)
        this.$message.error('加载盘点明细失败')
        this.loading = false
      })
    },

    // 加载分类列表
    loadCategoryList() {
      getAction('/materialCategory/list').then(res => {
        if (res.success) {
          this.categoryList = res.result || []
        }
      })
    },

    // 筛选处理
    handleFilter() {
      let filtered = [...this.dataSource]

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(item =>
          (item.materialName && item.materialName.toLowerCase().includes(keyword)) ||
          (item.materialModel && item.materialModel.toLowerCase().includes(keyword))
        )
      }

      // 差异类型筛选
      if (this.filterDiffType && this.filterDiffType !== 'all') {
        filtered = filtered.filter(item => {
          const diffNumber = parseFloat(item.diffNumber || 0)
          switch (this.filterDiffType) {
            case 'normal':
              return diffNumber === 0
            case 'positive':
              return diffNumber > 0
            case 'negative':
              return diffNumber < 0
            default:
              return true
          }
        })
      }

      // 分类筛选
      if (this.filterCategory) {
        filtered = filtered.filter(item => item.materialCategoryId === this.filterCategory)
      }

      this.filteredDataSource = filtered
    },

    // 初始化图表
    initCharts() {
      this.diffChart = echarts.init(this.$refs.diffChart)
      this.amountChart = echarts.init(this.$refs.amountChart)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },

    // 更新图表
    updateCharts() {
      this.updateDiffChart()
      this.updateAmountChart()
    },

    // 更新差异分布图表
    updateDiffChart() {
      if (!this.diffChart || !this.dataSource.length) return

      const normalCount = this.dataSource.filter(item => (item.diffNumber || 0) === 0).length
      const positiveCount = this.dataSource.filter(item => (item.diffNumber || 0) > 0).length
      const negativeCount = this.dataSource.filter(item => (item.diffNumber || 0) < 0).length

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['无差异', '盘盈', '盘亏']
        },
        series: [
          {
            name: '差异分布',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['60%', '50%'],
            data: [
              { value: normalCount, name: '无差异', itemStyle: { color: '#52c41a' } },
              { value: positiveCount, name: '盘盈', itemStyle: { color: '#1890ff' } },
              { value: negativeCount, name: '盘亏', itemStyle: { color: '#ff4d4f' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      this.diffChart.setOption(option)
    },

    // 更新金额分析图表
    updateAmountChart() {
      if (!this.amountChart || !this.dataSource.length) return

      const categories = [...new Set(this.dataSource.map(item => item.materialCategory))].slice(0, 10)
      const bookAmounts = categories.map(category => {
        return this.dataSource
          .filter(item => item.materialCategory === category)
          .reduce((sum, item) => sum + (parseFloat(item.bookAmount) || 0), 0)
      })
      const actualAmounts = categories.map(category => {
        return this.dataSource
          .filter(item => item.materialCategory === category)
          .reduce((sum, item) => sum + (parseFloat(item.actualAmount) || 0), 0)
      })

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['账面金额', '实盘金额']
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '¥{value}'
          }
        },
        series: [
          {
            name: '账面金额',
            type: 'bar',
            data: bookAmounts,
            itemStyle: { color: '#1890ff' }
          },
          {
            name: '实盘金额',
            type: 'bar',
            data: actualAmounts,
            itemStyle: { color: '#52c41a' }
          }
        ]
      }

      this.amountChart.setOption(option)
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.diffChart) {
        this.diffChart.resize()
      }
      if (this.amountChart) {
        this.amountChart.resize()
      }
    },

    // 导出报告
    handleExport() {
      this.exportLoading = true
      getAction(`/inventoryCheck/export`, { id: this.checkId }).then(res => {
        if (res.success) {
          this.$message.success('导出成功')
          // 下载文件
          window.open(res.result)
        } else {
          this.$message.error(res.message || '导出失败')
        }
        this.exportLoading = false
      }).catch(err => {
        console.error('导出失败:', err)
        this.$message.error('导出失败')
        this.exportLoading = false
      })
    },

    // 返回列表
    handleBack() {
      this.$router.push('/inventory/check/list')
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        '0': 'orange',
        '1': 'blue',
        '2': 'green',
        '3': 'red'
      }
      return colorMap[status] || 'default'
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        '0': 'edit',
        '1': 'clock-circle',
        '2': 'check-circle',
        '3': 'close-circle'
      }
      return iconMap[status] || 'question-circle'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        '0': '草稿',
        '1': '盘点中',
        '2': '已完成',
        '3': '已取消'
      }
      return textMap[status] || status
    },

    // 获取准确率样式类
    getAccuracyClass(accuracy) {
      if (!accuracy) return ''
      const rate = parseFloat(accuracy)
      if (rate >= 95) return 'accuracy-excellent'
      if (rate >= 90) return 'accuracy-good'
      if (rate >= 80) return 'accuracy-normal'
      return 'accuracy-poor'
    },

    // 获取差异样式类
    getDiffClass(value) {
      if (!value || value === 0) return 'diff-zero'
      return value > 0 ? 'diff-positive' : 'diff-negative'
    },

    // 格式化数字
    formatNumber(value) {
      if (!value || value === 0) return '0'
      return parseFloat(value).toFixed(3)
    },

    // 格式化差异数量
    formatDiffNumber(value) {
      if (!value || value === 0) return '0'
      return value > 0 ? `+${parseFloat(value).toFixed(3)}` : `${parseFloat(value).toFixed(3)}`
    },

    // 格式化金额
    formatMoney(value) {
      if (!value || value === 0) return '¥0.00'
      return `¥${parseFloat(value).toFixed(2)}`
    },

    // 格式化百分比
    formatPercent(value) {
      if (!value || value === 0) return '0%'
      return `${parseFloat(value).toFixed(2)}%`
    }
  },

  filters: {
    moment(date) {
      return date ? this.$moment(date).format('YYYY-MM-DD HH:mm') : '-'
    }
  }
}
</script>

<style scoped>
.inventory-check-detail {
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  margin-bottom: 24px;
}

.page-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: white;
}

.page-title p {
  margin: 8px 0 0 0;
  opacity: 0.9;
  font-size: 16px;
}

.page-actions {
  display: flex;
  gap: 12px;
}

/* 概览容器 */
.overview-container {
  max-width: 1400px;
  margin: 0 auto 24px auto;
  padding: 0 24px;
}

.info-card,
.stats-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 基本信息 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item .label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.info-item .value {
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.status-tag {
  align-self: flex-start;
  border-radius: 6px;
  font-weight: 500;
}

/* 统计信息 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.diff-count {
  color: #ff4d4f;
}

.accuracy-rate.accuracy-excellent {
  color: #52c41a;
}

.accuracy-rate.accuracy-good {
  color: #1890ff;
}

.accuracy-rate.accuracy-normal {
  color: #faad14;
}

.accuracy-rate.accuracy-poor {
  color: #ff4d4f;
}

.total-amount {
  color: #1890ff;
}

.diff-amount.diff-positive {
  color: #52c41a;
}

.diff-amount.diff-negative {
  color: #ff4d4f;
}

.diff-amount.diff-zero {
  color: #666;
}

/* 图表容器 */
.chart-container {
  max-width: 1400px;
  margin: 0 auto 24px auto;
  padding: 0 24px;
}

.chart-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart {
  width: 100%;
}

/* 明细容器 */
.detail-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.detail-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 筛选工具栏 */
.filter-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

/* 表格样式 */
.detail-table >>> .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.material-info {
  line-height: 1.4;
}

.material-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.material-model {
  font-size: 12px;
  color: #666;
}

.number-text {
  font-weight: 500;
  color: #333;
}

.diff-number,
.diff-amount,
.diff-rate {
  font-weight: 600;
}

.diff-zero {
  color: #666;
}

.diff-positive {
  color: #52c41a;
}

.diff-negative {
  color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header-content,
  .overview-container,
  .chart-container,
  .detail-container {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
  }

  .page-header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .page-actions {
    flex-direction: column;
    width: 100%;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .filter-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-toolbar > * {
    width: 100% !important;
    margin-left: 0 !important;
  }
}

/* 动画效果 */
.inventory-check-detail >>> .ant-table-tbody > tr:hover > td {
  background: #f5f7fa !important;
}

.inventory-check-detail >>> .ant-btn {
  border-radius: 6px;
}

.inventory-check-detail >>> .ant-card {
  border-radius: 12px;
}

.inventory-check-detail >>> .ant-input,
.inventory-check-detail >>> .ant-select-selector {
  border-radius: 6px;
}
</style>
