<template>
  <div class="inventory-check-input">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-header-content">
        <div class="page-title">
          <h2>📝 盘点录入</h2>
          <p>录入实际盘点数据，系统将自动计算差异</p>
        </div>
        <div class="page-actions">
          <a-button @click="handleBack">
            <a-icon type="arrow-left" />
            返回列表
          </a-button>
        </div>
      </div>
    </div>

    <!-- 盘点信息卡片 -->
    <div class="info-container">
      <a-card :bordered="false" class="info-card">
        <div class="check-info">
          <div class="info-item">
            <span class="label">盘点单号：</span>
            <span class="value">{{ checkInfo.number }}</span>
          </div>
          <div class="info-item">
            <span class="label">仓库：</span>
            <span class="value">{{ checkInfo.depotName }}</span>
          </div>
          <div class="info-item">
            <span class="label">状态：</span>
            <a-tag :color="getStatusColor(checkInfo.status)">
              {{ getStatusText(checkInfo.status) }}
            </a-tag>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ checkInfo.createTime | moment }}</span>
          </div>
        </div>
        
        <div class="progress-info">
          <div class="progress-item">
            <span class="progress-label">录入进度</span>
            <a-progress 
              :percent="inputProgress" 
              :status="inputProgress === 100 ? 'success' : 'active'"
            />
          </div>
        </div>
      </a-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar-container">
      <a-card :bordered="false" class="toolbar-card">
        <div class="toolbar">
          <div class="toolbar-left">
            <a-input-search
              v-model="searchKeyword"
              placeholder="搜索商品名称、规格、条码"
              style="width: 300px;"
              @search="handleSearch"
              @change="handleSearch"
            />
            <a-select
              v-model="filterCategory"
              placeholder="筛选分类"
              style="width: 150px; margin-left: 12px;"
              allowClear
              @change="handleFilter"
            >
              <a-select-option v-for="category in categoryList" :key="category.id" :value="category.id">
                {{ category.name }}
              </a-select-option>
            </a-select>
            <a-select
              v-model="filterStatus"
              placeholder="录入状态"
              style="width: 120px; margin-left: 12px;"
              allowClear
              @change="handleFilter"
            >
              <a-select-option value="pending">待录入</a-select-option>
              <a-select-option value="completed">已录入</a-select-option>
              <a-select-option value="diff">有差异</a-select-option>
            </a-select>
          </div>
          <div class="toolbar-right">
            <a-button type="primary" @click="handleBatchInput" :disabled="selectedRowKeys.length === 0">
              <a-icon type="edit" />
              批量录入 ({{ selectedRowKeys.length }})
            </a-button>
            <a-button @click="handleSaveAll" :loading="saveLoading">
              <a-icon type="save" />
              保存全部
            </a-button>
            <a-button type="primary" @click="handleComplete" :disabled="!canComplete">
              <a-icon type="check-circle" />
              完成盘点
            </a-button>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 盘点明细表格 -->
    <div class="table-container">
      <a-card :bordered="false" class="table-card">
        <a-table
          ref="table"
          rowKey="id"
          :columns="columns"
          :dataSource="filteredDataSource"
          :pagination="pagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :scroll="{ x: 1400 }"
          size="middle"
        >
          <template slot="materialInfo" slot-scope="text, record">
            <div class="material-info">
              <div class="material-name">{{ record.materialName }}</div>
              <div class="material-model">{{ record.materialModel }}</div>
            </div>
          </template>

          <template slot="bookNumber" slot-scope="text">
            <span class="number-text">{{ text || 0 }}</span>
          </template>

          <template slot="actualNumber" slot-scope="text, record">
            <a-input-number
              v-model="record.actualNumber"
              :min="0"
              :precision="3"
              :step="1"
              style="width: 120px;"
              @change="handleActualNumberChange(record)"
              @pressEnter="focusNextInput(record)"
            />
          </template>

          <template slot="diffNumber" slot-scope="text, record">
            <span 
              class="diff-number" 
              :class="getDiffClass(record.diffNumber)"
            >
              {{ formatDiffNumber(record.diffNumber) }}
            </span>
          </template>

          <template slot="diffAmount" slot-scope="text, record">
            <span 
              class="diff-amount" 
              :class="getDiffClass(record.diffAmount)"
            >
              {{ formatMoney(record.diffAmount) }}
            </span>
          </template>

          <template slot="inputStatus" slot-scope="text, record">
            <a-tag :color="getInputStatusColor(record)">
              {{ getInputStatusText(record) }}
            </a-tag>
          </template>

          <template slot="action" slot-scope="text, record">
            <a-button 
              type="link" 
              icon="edit" 
              @click="handleQuickInput(record)"
              size="small"
            >
              快速录入
            </a-button>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 快速录入模态框 -->
    <a-modal
      title="快速录入"
      :visible="quickInputVisible"
      @ok="handleQuickInputOk"
      @cancel="quickInputVisible = false"
      width="600px"
    >
      <div v-if="currentRecord" class="quick-input-form">
        <div class="material-info-detail">
          <h4>{{ currentRecord.materialName }}</h4>
          <p>规格：{{ currentRecord.materialModel }}</p>
          <p>单位：{{ currentRecord.materialUnit }}</p>
        </div>
        
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
          <a-form-item label="账面数量">
            <a-input-number 
              :value="currentRecord.bookNumber" 
              disabled 
              style="width: 100%;"
            />
          </a-form-item>
          <a-form-item label="实盘数量">
            <a-input-number
              v-model="quickInputNumber"
              :min="0"
              :precision="3"
              :step="1"
              style="width: 100%;"
              placeholder="请输入实盘数量"
              ref="quickInputRef"
            />
          </a-form-item>
          <a-form-item label="备注">
            <a-textarea
              v-model="quickInputRemark"
              placeholder="请输入备注"
              :rows="3"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 批量录入模态框 -->
    <a-modal
      title="批量录入"
      :visible="batchInputVisible"
      @ok="handleBatchInputOk"
      @cancel="batchInputVisible = false"
      width="500px"
    >
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="录入方式">
          <a-radio-group v-model="batchInputType">
            <a-radio value="same">统一数量</a-radio>
            <a-radio value="book">按账面数量</a-radio>
            <a-radio value="zero">全部为零</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="数量" v-if="batchInputType === 'same'">
          <a-input-number
            v-model="batchInputNumber"
            :min="0"
            :precision="3"
            style="width: 100%;"
            placeholder="请输入统一数量"
          />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea
            v-model="batchInputRemark"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'InventoryCheckInput',
  data() {
    return {
      // 盘点信息
      checkInfo: {},
      // 盘点明细数据
      dataSource: [],
      // 过滤后的数据
      filteredDataSource: [],
      // 加载状态
      loading: false,
      // 保存加载状态
      saveLoading: false,
      // 选中的行
      selectedRowKeys: [],
      // 搜索关键词
      searchKeyword: '',
      // 筛选分类
      filterCategory: null,
      // 筛选状态
      filterStatus: null,
      // 分类列表
      categoryList: [],
      // 快速录入显示状态
      quickInputVisible: false,
      // 当前录入记录
      currentRecord: null,
      // 快速录入数量
      quickInputNumber: null,
      // 快速录入备注
      quickInputRemark: '',
      // 批量录入显示状态
      batchInputVisible: false,
      // 批量录入类型
      batchInputType: 'same',
      // 批量录入数量
      batchInputNumber: null,
      // 批量录入备注
      batchInputRemark: '',
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 20,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      },
      // 表格列配置
      columns: [
        {
          title: '商品信息',
          dataIndex: 'materialInfo',
          width: 200,
          fixed: 'left',
          scopedSlots: { customRender: 'materialInfo' }
        },
        {
          title: '分类',
          dataIndex: 'materialCategory',
          width: 120
        },
        {
          title: '单位',
          dataIndex: 'materialUnit',
          width: 80,
          align: 'center'
        },
        {
          title: '账面数量',
          dataIndex: 'bookNumber',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'bookNumber' }
        },
        {
          title: '实盘数量',
          dataIndex: 'actualNumber',
          width: 140,
          align: 'center',
          scopedSlots: { customRender: 'actualNumber' }
        },
        {
          title: '差异数量',
          dataIndex: 'diffNumber',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'diffNumber' }
        },
        {
          title: '单价',
          dataIndex: 'unitPrice',
          width: 100,
          align: 'right',
          customRender: (text) => this.formatMoney(text)
        },
        {
          title: '差异金额',
          dataIndex: 'diffAmount',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'diffAmount' }
        },
        {
          title: '录入状态',
          dataIndex: 'inputStatus',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'inputStatus' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },

  computed: {
    // 录入进度
    inputProgress() {
      if (this.dataSource.length === 0) return 0
      const inputCount = this.dataSource.filter(item => item.actualNumber !== null && item.actualNumber !== undefined).length
      return Math.round((inputCount / this.dataSource.length) * 100)
    },

    // 是否可以完成盘点
    canComplete() {
      return this.inputProgress === 100
    }
  },

  created() {
    this.initPage()
  },

  methods: {
    // 初始化页面
    initPage() {
      const { id } = this.$route.params
      this.checkId = id
      this.loadCheckInfo()
      this.loadCheckDetails()
      this.loadCategoryList()
    },

    // 加载盘点信息
    loadCheckInfo() {
      getAction(`/inventoryCheck/detail/${this.checkId}`).then(res => {
        if (res.success) {
          this.checkInfo = res.result
        }
      }).catch(err => {
        console.error('加载盘点信息失败:', err)
        this.$message.error('加载盘点信息失败')
      })
    },

    // 加载盘点明细
    loadCheckDetails() {
      this.loading = true
      getAction(`/inventoryCheck/details/${this.checkId}`).then(res => {
        if (res.success) {
          this.dataSource = res.result || []
          this.handleFilter()
        }
        this.loading = false
      }).catch(err => {
        console.error('加载盘点明细失败:', err)
        this.$message.error('加载盘点明细失败')
        this.loading = false
      })
    },

    // 加载分类列表
    loadCategoryList() {
      getAction('/materialCategory/list').then(res => {
        if (res.success) {
          this.categoryList = res.result || []
        }
      })
    },

    // 搜索处理
    handleSearch() {
      this.handleFilter()
    },

    // 筛选处理
    handleFilter() {
      let filtered = [...this.dataSource]

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(item =>
          (item.materialName && item.materialName.toLowerCase().includes(keyword)) ||
          (item.materialModel && item.materialModel.toLowerCase().includes(keyword)) ||
          (item.sku && item.sku.toLowerCase().includes(keyword))
        )
      }

      // 分类筛选
      if (this.filterCategory) {
        filtered = filtered.filter(item => item.materialCategoryId === this.filterCategory)
      }

      // 状态筛选
      if (this.filterStatus) {
        filtered = filtered.filter(item => {
          const status = this.getInputStatus(item)
          return status === this.filterStatus
        })
      }

      this.filteredDataSource = filtered
    },

    // 实盘数量变化
    handleActualNumberChange(record) {
      this.calculateDiff(record)
    },

    // 计算差异
    calculateDiff(record) {
      const bookNumber = parseFloat(record.bookNumber || 0)
      const actualNumber = parseFloat(record.actualNumber || 0)
      const unitPrice = parseFloat(record.unitPrice || 0)

      record.diffNumber = actualNumber - bookNumber
      record.diffAmount = record.diffNumber * unitPrice
    },

    // 快速录入
    handleQuickInput(record) {
      this.currentRecord = record
      this.quickInputNumber = record.actualNumber
      this.quickInputRemark = record.remark || ''
      this.quickInputVisible = true

      this.$nextTick(() => {
        if (this.$refs.quickInputRef) {
          this.$refs.quickInputRef.focus()
        }
      })
    },

    // 快速录入确认
    handleQuickInputOk() {
      if (this.currentRecord) {
        this.currentRecord.actualNumber = this.quickInputNumber
        this.currentRecord.remark = this.quickInputRemark
        this.calculateDiff(this.currentRecord)
        this.quickInputVisible = false
      }
    },

    // 批量录入
    handleBatchInput() {
      this.batchInputVisible = true
    },

    // 批量录入确认
    handleBatchInputOk() {
      const selectedRecords = this.dataSource.filter(item =>
        this.selectedRowKeys.includes(item.id)
      )

      selectedRecords.forEach(record => {
        switch (this.batchInputType) {
          case 'same':
            record.actualNumber = this.batchInputNumber
            break
          case 'book':
            record.actualNumber = record.bookNumber
            break
          case 'zero':
            record.actualNumber = 0
            break
        }

        if (this.batchInputRemark) {
          record.remark = this.batchInputRemark
        }

        this.calculateDiff(record)
      })

      this.batchInputVisible = false
      this.selectedRowKeys = []
      this.$message.success(`批量录入 ${selectedRecords.length} 条数据成功`)
    },

    // 保存全部
    handleSaveAll() {
      const details = this.dataSource.map(item => ({
        id: item.id,
        actualNumber: item.actualNumber,
        remark: item.remark
      }))

      this.saveLoading = true
      postAction('/inventoryCheck/saveDetails', {
        checkId: this.checkId,
        details: details
      }).then(res => {
        if (res.success) {
          this.$message.success('保存成功')
          this.loadCheckDetails()
        } else {
          this.$message.error(res.message || '保存失败')
        }
        this.saveLoading = false
      }).catch(err => {
        console.error('保存失败:', err)
        this.$message.error('保存失败')
        this.saveLoading = false
      })
    },

    // 完成盘点
    handleComplete() {
      this.$confirm({
        title: '确认完成盘点？',
        content: '完成后将无法再修改盘点数据，确定要完成吗？',
        onOk: () => {
          postAction(`/inventoryCheck/complete/${this.checkId}`).then(res => {
            if (res.success) {
              this.$message.success('完成盘点成功')
              this.$router.push(`/inventory/check/detail/${this.checkId}`)
            } else {
              this.$message.error(res.message || '完成盘点失败')
            }
          }).catch(err => {
            console.error('完成盘点失败:', err)
            this.$message.error('完成盘点失败')
          })
        }
      })
    },

    // 选择变化
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },

    // 焦点到下一个输入框
    focusNextInput(record) {
      // 可以实现按回车键自动跳转到下一行的逻辑
    },

    // 返回列表
    handleBack() {
      this.$router.push('/inventory/check/list')
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        '0': 'orange',
        '1': 'blue',
        '2': 'green',
        '3': 'red'
      }
      return colorMap[status] || 'default'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        '0': '草稿',
        '1': '盘点中',
        '2': '已完成',
        '3': '已取消'
      }
      return textMap[status] || status
    },

    // 获取录入状态
    getInputStatus(record) {
      if (record.actualNumber === null || record.actualNumber === undefined) {
        return 'pending'
      }
      if (record.diffNumber === 0) {
        return 'completed'
      }
      return 'diff'
    },

    // 获取录入状态颜色
    getInputStatusColor(record) {
      const status = this.getInputStatus(record)
      const colorMap = {
        'pending': 'orange',
        'completed': 'green',
        'diff': 'red'
      }
      return colorMap[status] || 'default'
    },

    // 获取录入状态文本
    getInputStatusText(record) {
      const status = this.getInputStatus(record)
      const textMap = {
        'pending': '待录入',
        'completed': '已录入',
        'diff': '有差异'
      }
      return textMap[status] || status
    },

    // 获取差异样式类
    getDiffClass(value) {
      if (!value || value === 0) return 'diff-zero'
      return value > 0 ? 'diff-positive' : 'diff-negative'
    },

    // 格式化差异数量
    formatDiffNumber(value) {
      if (!value || value === 0) return '0'
      return value > 0 ? `+${value}` : `${value}`
    },

    // 格式化金额
    formatMoney(value) {
      if (!value || value === 0) return '¥0.00'
      return `¥${parseFloat(value).toFixed(2)}`
    }
  },

  filters: {
    moment(date) {
      return date ? this.$moment(date).format('YYYY-MM-DD HH:mm') : '-'
    }
  }
}
