<template>
  <div class="inventory-check-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-header-content">
        <div class="page-title">
          <h2>📋 库存盘点管理</h2>
          <p>管理和执行库存盘点任务，确保库存数据准确性</p>
        </div>
        <div class="page-actions">
          <a-button type="primary" icon="plus" size="large" @click="handleAdd">
            新增盘点
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <a-icon type="file-text" />
              </div>
              <div class="stat-info">
                <h3>{{ statistics.totalChecks || 0 }}</h3>
                <p>总盘点单</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <a-icon type="clock-circle" />
              </div>
              <div class="stat-info">
                <h3>{{ statistics.inProgressChecks || 0 }}</h3>
                <p>进行中</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <a-icon type="check-circle" />
              </div>
              <div class="stat-info">
                <h3>{{ statistics.completedChecks || 0 }}</h3>
                <p>已完成</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <a-icon type="percentage" />
              </div>
              <div class="stat-info">
                <h3>{{ statistics.avgAccuracy || '0' }}%</h3>
                <p>平均准确率</p>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 查询区域 -->
    <a-card :bordered="false" class="search-card">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :md="6" :sm="24">
              <a-form-item label="盘点单号">
                <a-input
                  v-model="queryParam.checkNo"
                  placeholder="请输入盘点单号"
                  allowClear
                  @pressEnter="searchQuery"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="仓库">
                <a-select v-model="queryParam.depotId" placeholder="请选择仓库" allowClear>
                  <a-select-option v-for="depot in depotList" :key="depot.id" :value="depot.id">
                    {{ depot.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.status" placeholder="请选择状态" allowClear>
                  <a-select-option value="0">草稿</a-select-option>
                  <a-select-option value="1">盘点中</a-select-option>
                  <a-select-option value="2">已完成</a-select-option>
                  <a-select-option value="3">已取消</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="创建时间">
                <a-range-picker
                  v-model="queryParam.dateRange"
                  format="YYYY-MM-DD"
                  placeholder="['开始日期', '结束日期']"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right;">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button style="margin-left: 8px" @click="searchReset" icon="reload">重置</a-button>
              <a-button style="margin-left: 8px" @click="handleExport" icon="download">导出</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>

    <!-- 数据表格 -->
    <a-card :bordered="false" class="table-card">
      <div class="table-operator">
        <div class="table-operator-left">
          <a-button
            type="danger"
            icon="delete"
            :disabled="selectedRowKeys.length === 0"
            @click="batchDel"
          >
            批量删除 ({{ selectedRowKeys.length }})
          </a-button>
        </div>
        <div class="table-operator-right">
          <a-tooltip title="刷新">
            <a-button icon="reload" @click="loadData" />
          </a-tooltip>
          <a-tooltip title="密度">
            <a-dropdown>
              <a-button icon="column-height" />
              <a-menu slot="overlay" @click="handleSizeChange">
                <a-menu-item key="default">默认</a-menu-item>
                <a-menu-item key="middle">中等</a-menu-item>
                <a-menu-item key="small">紧凑</a-menu-item>
              </a-menu>
            </a-dropdown>
          </a-tooltip>
        </div>
      </div>

      <a-table
        ref="table"
        :size="tableSize"
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <template slot="checkNo" slot-scope="text, record">
          <a @click="handleDetail(record)" class="link-text">{{ text }}</a>
        </template>

        <template slot="status" slot-scope="text">
          <a-tag :color="getStatusColor(text)" class="status-tag">
            <a-icon :type="getStatusIcon(text)" />
            {{ getStatusText(text) }}
          </a-tag>
        </template>

        <template slot="progress" slot-scope="text, record">
          <div class="progress-wrapper">
            <a-progress
              :percent="calculateProgress(record)"
              :status="getProgressStatus(record.status)"
              size="small"
              :showInfo="false"
            />
            <span class="progress-text">{{ calculateProgress(record) }}%</span>
          </div>
        </template>

        <template slot="accuracy" slot-scope="text">
          <span class="accuracy-text" :class="getAccuracyClass(text)">
            {{ text ? text + '%' : '-' }}
          </span>
        </template>

        <template slot="action" slot-scope="text, record">
          <div class="action-buttons">
            <a-tooltip title="查看详情">
              <a-button type="link" icon="eye" @click="handleDetail(record)" />
            </a-tooltip>

            <a-tooltip title="编辑" v-if="record.status === '0'">
              <a-button type="link" icon="edit" @click="handleEdit(record)" />
            </a-tooltip>

            <a-tooltip title="开始盘点" v-if="record.status === '0'">
              <a-button type="link" icon="play-circle" @click="handleStart(record)" style="color: #52c41a;" />
            </a-tooltip>

            <a-tooltip title="录入数据" v-if="record.status === '1'">
              <a-button type="link" icon="form" @click="handleCheck(record)" style="color: #1890ff;" />
            </a-tooltip>

            <a-tooltip title="完成盘点" v-if="record.status === '1'">
              <a-button type="link" icon="check-circle" @click="handleComplete(record)" style="color: #52c41a;" />
            </a-tooltip>

            <a-tooltip title="取消盘点" v-if="record.status === '1'">
              <a-button type="link" icon="close-circle" @click="handleCancel(record)" style="color: #ff4d4f;" />
            </a-tooltip>

            <a-tooltip title="删除" v-if="record.status === '0'">
              <a-popconfirm title="确定删除这个盘点单吗?" @confirm="() => handleDelete(record.id)">
                <a-button type="link" icon="delete" style="color: #ff4d4f;" />
              </a-popconfirm>
            </a-tooltip>
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑对话框 -->
    <inventory-check-modal
      ref="modalForm"
      @ok="modalFormOk"
    />
  </div>
</template>

<script>
import { getAction, deleteAction, postAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'InventoryCheckList',
  mixins: [JeecgListMixin],
  components: {
    // InventoryCheckModal
  },
  data() {
    return {
      description: '库存盘点管理',
      // 查询参数
      queryParam: {
        checkNo: '',
        depotId: '',
        status: '',
        dateRange: []
      },
      // 表格数据
      dataSource: [],
      // 统计数据
      statistics: {
        totalChecks: 0,
        inProgressChecks: 0,
        completedChecks: 0,
        avgAccuracy: 0
      },
      // 仓库列表
      depotList: [],
      // 表格大小
      tableSize: 'default',
      // 表格列配置
      columns: [
        {
          title: '盘点单号',
          dataIndex: 'checkNo',
          width: 150,
          scopedSlots: { customRender: 'checkNo' },
          fixed: 'left'
        },
        {
          title: '仓库',
          dataIndex: 'depotName',
          width: 120
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '盘点进度',
          dataIndex: 'progress',
          width: 120,
          scopedSlots: { customRender: 'progress' }
        },
        {
          title: '总数量',
          dataIndex: 'totalCount',
          width: 100,
          align: 'center'
        },
        {
          title: '差异数量',
          dataIndex: 'diffCount',
          width: 100,
          align: 'center'
        },
        {
          title: '准确率',
          dataIndex: 'accuracyRate',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'accuracy' }
        },
        {
          title: '创建人',
          dataIndex: 'creatorName',
          width: 100
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 150,
          customRender: (text) => {
            return text ? this.$moment(text).format('YYYY-MM-DD HH:mm') : '-'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 250,
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 分页配置
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      // 加载状态
      loading: false,
      // 选中的行
      selectedRowKeys: []
    }
  },
  
  mounted() {
    this.loadData()
    this.loadStatistics()
    this.loadDepotList()
  },

  methods: {
    // 加载数据
    loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      const params = Object.assign({}, this.queryParam, this.isorter)
      params.pageNo = this.ipagination.current
      params.pageSize = this.ipagination.pageSize

      this.loading = true

      // 调用真实API
      getAction('/inventoryCheck/list', params).then(res => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          this.ipagination.total = res.result.total || 0
        } else {
          this.$message.error(res.message || '查询失败')
        }
        this.loading = false
      }).catch(err => {
        console.error('查询盘点列表失败:', err)
        this.$message.error('查询失败')
        this.loading = false
      })
    },

    // 加载统计数据
    loadStatistics() {
      getAction('/inventoryCheck/statistics').then(res => {
        if (res.success) {
          this.statistics = res.result || {}
        }
      }).catch(err => {
        console.error('加载统计数据失败:', err)
      })
    },

    // 加载仓库列表
    loadDepotList() {
      getAction('/depot/list').then(res => {
        if (res.success) {
          this.depotList = res.result || []
        }
      }).catch(err => {
        console.error('加载仓库列表失败:', err)
      })
    },

    // 查询
    searchQuery() {
      this.loadData(1)
    },

    // 重置
    searchReset() {
      this.queryParam = {
        checkNo: '',
        depotId: '',
        status: '',
        dateRange: []
      }
      this.loadData(1)
    },

    // 新增
    handleAdd() {
      this.$router.push('/inventory/check/add')
    },

    // 编辑
    handleEdit(record) {
      this.$router.push(`/inventory/check/edit/${record.id}`)
    },

    // 详情
    handleDetail(record) {
      this.$router.push(`/inventory/check/detail/${record.id}`)
    },

    // 开始盘点
    handleStart(record) {
      this.$confirm({
        title: '确认开始盘点？',
        content: '开始盘点后将生成盘点明细，确定要开始吗？',
        onOk: () => {
          postAction(`/inventoryCheck/start/${record.id}`).then(res => {
            if (res.success) {
              this.$message.success('开始盘点成功')
              this.loadData()
              this.loadStatistics()
            } else {
              this.$message.error(res.message || '开始盘点失败')
            }
          }).catch(err => {
            console.error('开始盘点失败:', err)
            this.$message.error('开始盘点失败')
          })
        }
      })
    },

    // 盘点录入
    handleCheck(record) {
      this.$router.push(`/inventory/check/input/${record.id}`)
    },

    // 完成盘点
    handleComplete(record) {
      this.$confirm({
        title: '确认完成盘点？',
        content: '完成后将无法再修改盘点数据，确定要完成吗？',
        onOk: () => {
          postAction(`/inventoryCheck/complete/${record.id}`).then(res => {
            if (res.success) {
              this.$message.success('完成盘点成功')
              this.loadData()
              this.loadStatistics()
            } else {
              this.$message.error(res.message || '完成盘点失败')
            }
          }).catch(err => {
            console.error('完成盘点失败:', err)
            this.$message.error('完成盘点失败')
          })
        }
      })
    },

    // 取消盘点
    handleCancel(record) {
      this.$confirm({
        title: '确认取消盘点？',
        content: '取消后盘点数据将被清空，确定要取消吗？',
        onOk: () => {
          postAction(`/inventoryCheck/cancel/${record.id}`).then(res => {
            if (res.success) {
              this.$message.success('取消盘点成功')
              this.loadData()
              this.loadStatistics()
            } else {
              this.$message.error(res.message || '取消盘点失败')
            }
          }).catch(err => {
            console.error('取消盘点失败:', err)
            this.$message.error('取消盘点失败')
          })
        }
      })
    },

    // 删除
    handleDelete(id) {
      deleteAction('/inventoryCheck/delete', { ids: id }).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.loadData()
          this.loadStatistics()
        } else {
          this.$message.error(res.message || '删除失败')
        }
      }).catch(err => {
        console.error('删除失败:', err)
        this.$message.error('删除失败')
      })
    },

    // 批量删除
    batchDel() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      this.$confirm({
        title: '确认删除？',
        content: `确定要删除选中的 ${this.selectedRowKeys.length} 条数据吗？`,
        onOk: () => {
          const ids = this.selectedRowKeys.join(',')
          deleteAction('/inventoryCheck/delete', { ids }).then(res => {
            if (res.success) {
              this.$message.success('批量删除成功')
              this.selectedRowKeys = []
              this.loadData()
              this.loadStatistics()
            } else {
              this.$message.error(res.message || '批量删除失败')
            }
          }).catch(err => {
            console.error('批量删除失败:', err)
            this.$message.error('批量删除失败')
          })
        }
      })
    },

    // 导出
    handleExport() {
      const params = Object.assign({}, this.queryParam)
      getAction('/inventoryCheck/export', params).then(res => {
        if (res.success) {
          this.$message.success('导出成功')
          // 下载文件
          window.open(res.result)
        } else {
          this.$message.error(res.message || '导出失败')
        }
      }).catch(err => {
        console.error('导出失败:', err)
        this.$message.error('导出失败')
      })
    },

    // 表格变化
    handleTableChange(pagination, filters, sorter) {
      this.ipagination = pagination
      this.isorter = sorter
      this.loadData()
    },

    // 选择变化
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },

    // 表格大小变化
    handleSizeChange({ key }) {
      this.tableSize = key
    },

    // 模态框确认
    modalFormOk() {
      this.loadData()
      this.loadStatistics()
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        '0': 'orange',
        '1': 'blue',
        '2': 'green',
        '3': 'red'
      }
      return colorMap[status] || 'default'
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        '0': 'edit',
        '1': 'clock-circle',
        '2': 'check-circle',
        '3': 'close-circle'
      }
      return iconMap[status] || 'question-circle'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        '0': '草稿',
        '1': '盘点中',
        '2': '已完成',
        '3': '已取消'
      }
      return textMap[status] || status
    },

    // 计算进度
    calculateProgress(record) {
      if (!record.totalCount || record.totalCount === 0) {
        return 0
      }
      // 这里可以根据实际业务逻辑计算进度
      // 例如：已盘点数量 / 总数量 * 100
      return Math.round((record.totalCount - (record.diffCount || 0)) / record.totalCount * 100)
    },

    // 获取进度状态
    getProgressStatus(status) {
      const statusMap = {
        '0': 'normal',
        '1': 'active',
        '2': 'success',
        '3': 'exception'
      }
      return statusMap[status] || 'normal'
    },

    // 获取准确率样式类
    getAccuracyClass(accuracy) {
      if (!accuracy) return ''
      const rate = parseFloat(accuracy)
      if (rate >= 95) return 'accuracy-excellent'
      if (rate >= 90) return 'accuracy-good'
      if (rate >= 80) return 'accuracy-normal'
      return 'accuracy-poor'
    }
  }
  }
}
</script>

<style scoped>
.inventory-check-list {
  padding: 0;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  margin-bottom: 24px;
}

.page-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: white;
}

.page-title p {
  margin: 8px 0 0 0;
  opacity: 0.9;
  font-size: 16px;
}

/* 统计卡片 */
.statistics-cards {
  margin: 0 24px 24px 24px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 24px;
}

.stat-info h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
}

/* 搜索卡片 */
.search-card {
  margin: 0 24px 24px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格卡片 */
.table-card {
  margin: 0 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-operator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-operator-left {
  display: flex;
  gap: 8px;
}

.table-operator-right {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
.link-text {
  color: #1890ff;
  font-weight: 500;
}

.link-text:hover {
  color: #40a9ff;
}

.status-tag {
  border-radius: 6px;
  font-weight: 500;
}

.progress-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 35px;
}

.accuracy-text {
  font-weight: 500;
}

.accuracy-excellent {
  color: #52c41a;
}

.accuracy-good {
  color: #1890ff;
}

.accuracy-normal {
  color: #faad14;
}

.accuracy-poor {
  color: #ff4d4f;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
  }

  .page-header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .statistics-cards,
  .search-card,
  .table-card {
    margin: 0 16px 16px 16px;
  }

  .stat-content {
    justify-content: center;
  }

  .table-operator {
    flex-direction: column;
    gap: 12px;
  }
}

/* 动画效果 */
.inventory-check-list >>> .ant-table-tbody > tr:hover > td {
  background: #f5f7fa !important;
}

.inventory-check-list >>> .ant-btn {
  border-radius: 6px;
}

.inventory-check-list >>> .ant-card {
  border-radius: 12px;
}

.inventory-check-list >>> .ant-input,
.inventory-check-list >>> .ant-select-selector {
  border-radius: 6px;
}
</style>
