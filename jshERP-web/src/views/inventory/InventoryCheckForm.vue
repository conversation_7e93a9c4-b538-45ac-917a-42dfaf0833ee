<template>
  <div class="inventory-check-form">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-header-content">
        <div class="page-title">
          <h2>{{ isEdit ? '📝 编辑盘点单' : '➕ 新增盘点单' }}</h2>
          <p>{{ isEdit ? '修改盘点单信息和范围' : '创建新的库存盘点任务' }}</p>
        </div>
        <div class="page-actions">
          <a-button @click="handleBack">
            <a-icon type="arrow-left" />
            返回列表
          </a-button>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <a-card :bordered="false" class="form-card">
        <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">
              <a-icon type="info-circle" />
              基本信息
            </h3>
            
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="盘点单号">
                  <a-input 
                    v-decorator="['number', { rules: [{ required: true, message: '请输入盘点单号' }] }]"
                    placeholder="系统自动生成"
                    :disabled="isEdit"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="仓库">
                  <a-select 
                    v-decorator="['depotId', { rules: [{ required: true, message: '请选择仓库' }] }]"
                    placeholder="请选择仓库"
                    @change="handleDepotChange"
                  >
                    <a-select-option v-for="depot in depotList" :key="depot.id" :value="depot.id">
                      {{ depot.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="盘点类型">
                  <a-select 
                    v-decorator="['checkType', { initialValue: 'full' }]"
                    placeholder="请选择盘点类型"
                  >
                    <a-select-option value="full">全盘</a-select-option>
                    <a-select-option value="partial">部分盘点</a-select-option>
                    <a-select-option value="cycle">循环盘点</a-select-option>
                    <a-select-option value="spot">抽盘</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="盘点方式">
                  <a-select 
                    v-decorator="['checkMode', { initialValue: 'manual' }]"
                    placeholder="请选择盘点方式"
                  >
                    <a-select-option value="manual">手动盘点</a-select-option>
                    <a-select-option value="scan">扫描盘点</a-select-option>
                    <a-select-option value="auto">自动盘点</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="备注">
              <a-textarea 
                v-decorator="['remark']"
                placeholder="请输入备注信息"
                :rows="3"
              />
            </a-form-item>
          </div>

          <!-- 盘点范围 -->
          <div class="form-section">
            <h3 class="section-title">
              <a-icon type="filter" />
              盘点范围
            </h3>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="商品分类">
                  <a-tree-select
                    v-decorator="['categoryIds']"
                    :tree-data="categoryTree"
                    :multiple="true"
                    :show-checked-strategy="SHOW_PARENT"
                    placeholder="请选择商品分类"
                    tree-checkable
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="供应商">
                  <a-select 
                    v-decorator="['supplierIds']"
                    mode="multiple"
                    placeholder="请选择供应商"
                    allow-clear
                  >
                    <a-select-option v-for="supplier in supplierList" :key="supplier.id" :value="supplier.id">
                      {{ supplier.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="指定商品">
              <a-button type="dashed" @click="showMaterialSelector" block>
                <a-icon type="plus" />
                选择商品 (已选择 {{ selectedMaterials.length }} 个)
              </a-button>
              
              <div v-if="selectedMaterials.length > 0" class="selected-materials">
                <a-tag 
                  v-for="material in selectedMaterials" 
                  :key="material.id"
                  closable
                  @close="removeMaterial(material.id)"
                  class="material-tag"
                >
                  {{ material.name }} - {{ material.model }}
                </a-tag>
              </div>
            </a-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <a-button type="primary" @click="handleSubmit" :loading="submitLoading">
              <a-icon type="save" />
              {{ isEdit ? '更新' : '保存' }}
            </a-button>
            <a-button @click="handleSaveAndStart" :loading="submitLoading" style="margin-left: 8px;">
              <a-icon type="play-circle" />
              保存并开始盘点
            </a-button>
            <a-button @click="handleBack" style="margin-left: 8px;">
              取消
            </a-button>
          </div>
        </a-form>
      </a-card>
    </div>

    <!-- 商品选择器 -->
    <a-modal
      title="选择商品"
      :visible="materialSelectorVisible"
      :width="1000"
      @ok="handleMaterialSelectorOk"
      @cancel="materialSelectorVisible = false"
    >
      <material-selector
        ref="materialSelector"
        :selected-materials="selectedMaterials"
        @change="handleMaterialChange"
      />
    </a-modal>
  </div>
</template>

<script>
import { getAction, postAction, putAction } from '@/api/manage'
import MaterialSelector from '@/components/MaterialSelector'

export default {
  name: 'InventoryCheckForm',
  components: {
    MaterialSelector
  },
  data() {
    return {
      // 表单对象
      form: this.$form.createForm(this),
      // 是否编辑模式
      isEdit: false,
      // 记录ID
      recordId: null,
      // 提交加载状态
      submitLoading: false,
      // 仓库列表
      depotList: [],
      // 分类树
      categoryTree: [],
      // 供应商列表
      supplierList: [],
      // 选中的商品
      selectedMaterials: [],
      // 商品选择器显示状态
      materialSelectorVisible: false,
      // 树选择策略
      SHOW_PARENT: 'SHOW_PARENT'
    }
  },

  created() {
    this.initPage()
  },

  methods: {
    // 初始化页面
    initPage() {
      const { id } = this.$route.params
      this.isEdit = !!id
      this.recordId = id

      this.loadDepotList()
      this.loadCategoryTree()
      this.loadSupplierList()

      if (this.isEdit) {
        this.loadRecord()
      }
    },

    // 加载仓库列表
    loadDepotList() {
      getAction('/depot/list').then(res => {
        if (res.success) {
          this.depotList = res.result || []
        }
      }).catch(err => {
        console.error('加载仓库列表失败:', err)
      })
    },

    // 加载分类树
    loadCategoryTree() {
      getAction('/materialCategory/tree').then(res => {
        if (res.success) {
          this.categoryTree = res.result || []
        }
      }).catch(err => {
        console.error('加载分类树失败:', err)
      })
    },

    // 加载供应商列表
    loadSupplierList() {
      getAction('/supplier/list').then(res => {
        if (res.success) {
          this.supplierList = res.result || []
        }
      }).catch(err => {
        console.error('加载供应商列表失败:', err)
      })
    },

    // 加载记录
    loadRecord() {
      getAction(`/inventoryCheck/detail/${this.recordId}`).then(res => {
        if (res.success) {
          const record = res.result
          this.form.setFieldsValue({
            number: record.number,
            depotId: record.depotId,
            checkType: record.checkType,
            checkMode: record.checkMode,
            remark: record.remark,
            categoryIds: record.categoryIds ? JSON.parse(record.categoryIds) : [],
            supplierIds: record.supplierIds ? JSON.parse(record.supplierIds) : []
          })
          
          if (record.materialIds) {
            this.loadSelectedMaterials(JSON.parse(record.materialIds))
          }
        }
      }).catch(err => {
        console.error('加载记录失败:', err)
        this.$message.error('加载记录失败')
      })
    },

    // 加载选中的商品
    loadSelectedMaterials(materialIds) {
      if (materialIds && materialIds.length > 0) {
        getAction('/material/listByIds', { ids: materialIds.join(',') }).then(res => {
          if (res.success) {
            this.selectedMaterials = res.result || []
          }
        })
      }
    },

    // 仓库变化
    handleDepotChange(depotId) {
      // 可以根据仓库加载对应的库存商品
    },

    // 显示商品选择器
    showMaterialSelector() {
      this.materialSelectorVisible = true
    },

    // 商品变化
    handleMaterialChange(materials) {
      this.selectedMaterials = materials
    },

    // 商品选择器确认
    handleMaterialSelectorOk() {
      this.materialSelectorVisible = false
    },

    // 移除商品
    removeMaterial(materialId) {
      this.selectedMaterials = this.selectedMaterials.filter(m => m.id !== materialId)
    },

    // 提交表单
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.submitForm(values, false)
        }
      })
    },

    // 保存并开始盘点
    handleSaveAndStart() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.submitForm(values, true)
        }
      })
    },

    // 提交表单数据
    submitForm(values, startCheck = false) {
      this.submitLoading = true

      const formData = {
        ...values,
        categoryIds: values.categoryIds ? JSON.stringify(values.categoryIds) : null,
        supplierIds: values.supplierIds ? JSON.stringify(values.supplierIds) : null,
        materialIds: this.selectedMaterials.length > 0 ? JSON.stringify(this.selectedMaterials.map(m => m.id)) : null
      }

      const action = this.isEdit ? putAction : postAction
      const url = this.isEdit ? '/inventoryCheck/update' : '/inventoryCheck/add'
      
      if (this.isEdit) {
        formData.id = this.recordId
      }

      action(url, formData).then(res => {
        if (res.success) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功')
          
          if (startCheck && !this.isEdit) {
            // 如果是新增并且要开始盘点，则调用开始盘点接口
            const checkId = res.result.id || res.result
            this.startCheck(checkId)
          } else {
            this.handleBack()
          }
        } else {
          this.$message.error(res.message || (this.isEdit ? '更新失败' : '创建失败'))
        }
        this.submitLoading = false
      }).catch(err => {
        console.error('提交失败:', err)
        this.$message.error(this.isEdit ? '更新失败' : '创建失败')
        this.submitLoading = false
      })
    },

    // 开始盘点
    startCheck(checkId) {
      postAction(`/inventoryCheck/start/${checkId}`).then(res => {
        if (res.success) {
          this.$message.success('开始盘点成功')
          this.$router.push(`/inventory/check/input/${checkId}`)
        } else {
          this.$message.error(res.message || '开始盘点失败')
          this.handleBack()
        }
      }).catch(err => {
        console.error('开始盘点失败:', err)
        this.$message.error('开始盘点失败')
        this.handleBack()
      })
    },

    // 返回列表
    handleBack() {
      this.$router.push('/inventory/check/list')
    }
  }
}
</script>

<style scoped>
.inventory-check-form {
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  margin-bottom: 24px;
}

.page-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: white;
}

.page-title p {
  margin: 8px 0 0 0;
  opacity: 0.9;
  font-size: 16px;
}

/* 表单容器 */
.form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 24px;
}

.form-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表单区块 */
.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 选中的商品 */
.selected-materials {
  margin-top: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.material-tag {
  margin: 4px 8px 4px 0;
  padding: 4px 8px;
  border-radius: 6px;
}

/* 操作按钮 */
.form-actions {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
  }
  
  .page-header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .form-container {
    padding: 0 16px;
  }
}
</style>
