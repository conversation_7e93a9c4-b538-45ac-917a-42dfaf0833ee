<template>
  <div class="employee-preferences">
    <div class="preferences-header">
      <h3>员工偏好设置</h3>
      <p>配置员工的工作偏好和约束条件</p>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" icon="plus" @click="showAddPreference">
            添加偏好
          </a-button>
          <a-button icon="import" @click="importPreferences">
            批量导入
          </a-button>
          <a-button icon="export" @click="exportPreferences">
            导出配置
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 员工偏好列表 -->
    <div class="preferences-list">
      <a-card 
        v-for="employee in employees" 
        :key="employee.id"
        class="employee-card"
        :title="employee.name"
      >
        <template #extra>
          <a-space>
            <a-button type="link" size="small" @click="editPreference(employee)">
              编辑
            </a-button>
            <a-button type="link" size="small" @click="copyPreference(employee)">
              复制
            </a-button>
            <a-popconfirm
              title="确定要重置该员工的偏好设置吗？"
              @confirm="resetPreference(employee.id)"
            >
              <a-button type="link" size="small" danger>
                重置
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>

        <div class="preference-content">
          <!-- 工作时间偏好 -->
          <div class="preference-section">
            <h4>工作时间偏好</h4>
            <div class="time-preferences">
              <div class="week-schedule">
                <div 
                  v-for="day in weekDays" 
                  :key="day.key"
                  class="day-schedule"
                >
                  <div class="day-label">{{ day.label }}</div>
                  <div class="time-slots">
                    <a-tag 
                      v-for="slot in getAvailableSlots(employee.id, day.key)"
                      :key="slot.id"
                      :color="slot.available ? 'green' : 'red'"
                      class="time-slot"
                    >
                      {{ slot.startTime }}-{{ slot.endTime }}
                    </a-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 班次偏好 -->
          <div class="preference-section">
            <h4>班次偏好</h4>
            <a-row :gutter="16">
              <a-col :span="12">
                <div class="preference-group">
                  <label>偏好班次</label>
                  <div class="shift-tags">
                    <a-tag 
                      v-for="shift in getPreferredShifts(employee.id)"
                      :key="shift"
                      color="blue"
                    >
                      {{ shift }}
                    </a-tag>
                  </div>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="preference-group">
                  <label>避免班次</label>
                  <div class="shift-tags">
                    <a-tag 
                      v-for="shift in getAvoidShifts(employee.id)"
                      :key="shift"
                      color="red"
                    >
                      {{ shift }}
                    </a-tag>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>

          <!-- 工作量限制 -->
          <div class="preference-section">
            <h4>工作量限制</h4>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic 
                  title="最大连续天数" 
                  :value="getMaxConsecutiveDays(employee.id)"
                  suffix="天"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic 
                  title="每周最大工时" 
                  :value="getMaxWeeklyHours(employee.id)"
                  suffix="小时"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic 
                  title="偏好休息日" 
                  :value="getPreferredDaysOff(employee.id).length"
                  suffix="天"
                />
              </a-col>
            </a-row>
          </div>

          <!-- 特殊要求 -->
          <div class="preference-section">
            <h4>特殊要求</h4>
            <div class="special-requirements">
              <a-tag 
                v-for="req in getSpecialRequirements(employee.id)"
                :key="req.id"
                :color="req.type === 'medical' ? 'orange' : 'purple'"
              >
                {{ req.description }}
              </a-tag>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 偏好设置弹窗 -->
    <a-modal
      v-model="preferenceModalVisible"
      :title="modalTitle"
      width="800px"
      @ok="savePreference"
      @cancel="cancelPreference"
    >
      <a-form :form="preferenceForm" layout="vertical">
        <!-- 员工选择 -->
        <a-form-item label="员工">
          <a-select 
            v-decorator="['employeeId', { rules: [{ required: true, message: '请选择员工' }] }]"
            placeholder="选择员工"
            :disabled="editMode"
          >
            <a-select-option 
              v-for="emp in employees" 
              :key="emp.id" 
              :value="emp.id"
            >
              {{ emp.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 工作时间设置 -->
        <a-form-item label="工作时间偏好">
          <div class="time-preference-editor">
            <div 
              v-for="day in weekDays" 
              :key="day.key"
              class="day-editor"
            >
              <div class="day-header">
                <span>{{ day.label }}</span>
                <a-switch 
                  v-model="workingDays[day.key]"
                  checked-children="工作"
                  un-checked-children="休息"
                />
              </div>
              <div v-if="workingDays[day.key]" class="time-range-editor">
                <a-time-picker 
                  v-model="workingHours[day.key].start"
                  format="HH:mm"
                  placeholder="开始时间"
                />
                <span class="time-separator">至</span>
                <a-time-picker 
                  v-model="workingHours[day.key].end"
                  format="HH:mm"
                  placeholder="结束时间"
                />
              </div>
            </div>
          </div>
        </a-form-item>

        <!-- 班次偏好 -->
        <a-form-item label="班次偏好">
          <a-row :gutter="16">
            <a-col :span="12">
              <label>偏好班次</label>
              <a-select
                v-decorator="['preferredShifts']"
                mode="multiple"
                placeholder="选择偏好班次"
              >
                <a-select-option value="早班">早班</a-select-option>
                <a-select-option value="晚班">晚班</a-select-option>
                <a-select-option value="全天">全天</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <label>避免班次</label>
              <a-select
                v-decorator="['avoidShifts']"
                mode="multiple"
                placeholder="选择避免班次"
              >
                <a-select-option value="早班">早班</a-select-option>
                <a-select-option value="晚班">晚班</a-select-option>
                <a-select-option value="全天">全天</a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-item>

        <!-- 工作量限制 -->
        <a-form-item label="工作量限制">
          <a-row :gutter="16">
            <a-col :span="8">
              <label>最大连续工作天数</label>
              <a-input-number
                v-decorator="['maxConsecutiveDays', { initialValue: 5 }]"
                :min="1"
                :max="7"
                style="width: 100%"
              />
            </a-col>
            <a-col :span="8">
              <label>每周最大工时</label>
              <a-input-number
                v-decorator="['maxWeeklyHours', { initialValue: 40 }]"
                :min="1"
                :max="60"
                style="width: 100%"
              />
            </a-col>
            <a-col :span="8">
              <label>最少休息间隔(小时)</label>
              <a-input-number
                v-decorator="['minRestHours', { initialValue: 12 }]"
                :min="8"
                :max="24"
                style="width: 100%"
              />
            </a-col>
          </a-row>
        </a-form-item>

        <!-- 特殊要求 -->
        <a-form-item label="特殊要求">
          <div class="special-requirements-editor">
            <div 
              v-for="(req, index) in specialRequirements" 
              :key="index"
              class="requirement-item"
            >
              <a-select v-model="req.type" style="width: 120px">
                <a-select-option value="medical">医疗</a-select-option>
                <a-select-option value="family">家庭</a-select-option>
                <a-select-option value="education">教育</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
              <a-input 
                v-model="req.description" 
                placeholder="描述特殊要求"
                style="flex: 1; margin: 0 8px"
              />
              <a-button 
                type="link" 
                icon="delete" 
                @click="removeRequirement(index)"
              />
            </div>
            <a-button 
              type="dashed" 
              icon="plus" 
              @click="addRequirement"
              style="width: 100%"
            >
              添加特殊要求
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量导入弹窗 -->
    <a-modal
      v-model="importModalVisible"
      title="批量导入员工偏好"
      @ok="handleImport"
      @cancel="importModalVisible = false"
    >
      <a-upload-dragger
        :file-list="fileList"
        :before-upload="beforeUpload"
        @remove="handleRemove"
      >
        <p class="ant-upload-drag-icon">
          <a-icon type="inbox" />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">
          支持Excel文件格式，请使用标准模板
        </p>
      </a-upload-dragger>
      <div style="margin-top: 16px">
        <a-button type="link" @click="downloadTemplate">
          下载导入模板
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'EmployeePreferences',
  
  data() {
    return {
      // 员工列表
      employees: [
        { id: 18, name: '聆花老师' },
        { id: 19, name: '梁朝伟' },
        { id: 20, name: '龚锦华' },
        { id: 21, name: '伍尚明' },
        { id: 22, name: '莫智华' }
      ],
      
      // 周天数据
      weekDays: [
        { key: 'monday', label: '周一' },
        { key: 'tuesday', label: '周二' },
        { key: 'wednesday', label: '周三' },
        { key: 'thursday', label: '周四' },
        { key: 'friday', label: '周五' },
        { key: 'saturday', label: '周六' },
        { key: 'sunday', label: '周日' }
      ],
      
      // 员工偏好数据
      employeePreferences: {},
      
      // 弹窗状态
      preferenceModalVisible: false,
      importModalVisible: false,
      editMode: false,
      currentEmployee: null,
      
      // 表单数据
      preferenceForm: this.$form.createForm(this),
      workingDays: {},
      workingHours: {},
      specialRequirements: [],
      
      // 文件上传
      fileList: []
    }
  },
  
  computed: {
    modalTitle() {
      return this.editMode ? '编辑员工偏好' : '添加员工偏好'
    }
  },
  
  mounted() {
    this.loadEmployeePreferences()
    this.initializeWorkingData()
  },
  
  methods: {
    // 加载员工偏好数据
    loadEmployeePreferences() {
      // 模拟数据
      this.employeePreferences = {
        18: {
          availability: {
            monday: { available: true, start: '09:00', end: '17:00' },
            tuesday: { available: true, start: '09:00', end: '17:00' },
            wednesday: { available: true, start: '09:00', end: '17:00' },
            thursday: { available: true, start: '09:00', end: '17:00' },
            friday: { available: true, start: '09:00', end: '17:00' },
            saturday: { available: false },
            sunday: { available: false }
          },
          preferredShifts: ['早班'],
          avoidShifts: ['全天'],
          maxConsecutiveDays: 5,
          maxWeeklyHours: 40,
          minRestHours: 12,
          preferredDaysOff: ['saturday', 'sunday'],
          specialRequirements: [
            { type: 'family', description: '需要接送孩子上学' }
          ]
        }
      }
    },
    
    // 初始化工作数据
    initializeWorkingData() {
      this.weekDays.forEach(day => {
        this.$set(this.workingDays, day.key, true)
        this.$set(this.workingHours, day.key, {
          start: moment('09:00', 'HH:mm'),
          end: moment('17:00', 'HH:mm')
        })
      })
    },
    
    // 获取员工可用时段
    getAvailableSlots(employeeId, dayKey) {
      const preferences = this.employeePreferences[employeeId]
      if (!preferences || !preferences.availability) return []
      
      const dayAvailability = preferences.availability[dayKey]
      if (!dayAvailability || !dayAvailability.available) return []
      
      return [{
        id: 1,
        available: true,
        startTime: dayAvailability.start,
        endTime: dayAvailability.end
      }]
    },
    
    // 获取偏好班次
    getPreferredShifts(employeeId) {
      const preferences = this.employeePreferences[employeeId]
      return preferences?.preferredShifts || []
    },
    
    // 获取避免班次
    getAvoidShifts(employeeId) {
      const preferences = this.employeePreferences[employeeId]
      return preferences?.avoidShifts || []
    },
    
    // 获取最大连续天数
    getMaxConsecutiveDays(employeeId) {
      const preferences = this.employeePreferences[employeeId]
      return preferences?.maxConsecutiveDays || 5
    },
    
    // 获取每周最大工时
    getMaxWeeklyHours(employeeId) {
      const preferences = this.employeePreferences[employeeId]
      return preferences?.maxWeeklyHours || 40
    },
    
    // 获取偏好休息日
    getPreferredDaysOff(employeeId) {
      const preferences = this.employeePreferences[employeeId]
      return preferences?.preferredDaysOff || []
    },
    
    // 获取特殊要求
    getSpecialRequirements(employeeId) {
      const preferences = this.employeePreferences[employeeId]
      return preferences?.specialRequirements || []
    },
    
    // 显示添加偏好弹窗
    showAddPreference() {
      this.editMode = false
      this.currentEmployee = null
      this.preferenceModalVisible = true
      this.resetForm()
    },
    
    // 编辑偏好
    editPreference(employee) {
      this.editMode = true
      this.currentEmployee = employee
      this.preferenceModalVisible = true
      this.loadPreferenceData(employee.id)
    },
    
    // 复制偏好
    copyPreference(employee) {
      this.$confirm({
        title: '复制员工偏好',
        content: '请选择要复制到的员工',
        onOk: () => {
          // 实现复制逻辑
          this.$message.success('偏好复制成功')
        }
      })
    },
    
    // 重置偏好
    resetPreference(employeeId) {
      delete this.employeePreferences[employeeId]
      this.$message.success('偏好重置成功')
    },
    
    // 加载偏好数据到表单
    loadPreferenceData(employeeId) {
      const preferences = this.employeePreferences[employeeId]
      if (preferences) {
        // 设置表单值
        this.$nextTick(() => {
          this.preferenceForm.setFieldsValue({
            employeeId: employeeId,
            preferredShifts: preferences.preferredShifts,
            avoidShifts: preferences.avoidShifts,
            maxConsecutiveDays: preferences.maxConsecutiveDays,
            maxWeeklyHours: preferences.maxWeeklyHours,
            minRestHours: preferences.minRestHours
          })
        })
        
        // 设置工作时间
        if (preferences.availability) {
          this.weekDays.forEach(day => {
            const dayAvailability = preferences.availability[day.key]
            if (dayAvailability) {
              this.workingDays[day.key] = dayAvailability.available
              if (dayAvailability.available) {
                this.workingHours[day.key] = {
                  start: moment(dayAvailability.start, 'HH:mm'),
                  end: moment(dayAvailability.end, 'HH:mm')
                }
              }
            }
          })
        }
        
        // 设置特殊要求
        this.specialRequirements = [...(preferences.specialRequirements || [])]
      }
    },
    
    // 重置表单
    resetForm() {
      this.preferenceForm.resetFields()
      this.initializeWorkingData()
      this.specialRequirements = []
    },
    
    // 保存偏好
    savePreference() {
      this.preferenceForm.validateFields((err, values) => {
        if (!err) {
          const employeeId = values.employeeId
          
          // 构建偏好数据
          const preferences = {
            availability: {},
            preferredShifts: values.preferredShifts || [],
            avoidShifts: values.avoidShifts || [],
            maxConsecutiveDays: values.maxConsecutiveDays,
            maxWeeklyHours: values.maxWeeklyHours,
            minRestHours: values.minRestHours,
            specialRequirements: this.specialRequirements
          }
          
          // 设置可用时间
          this.weekDays.forEach(day => {
            preferences.availability[day.key] = {
              available: this.workingDays[day.key],
              start: this.workingDays[day.key] ? this.workingHours[day.key].start.format('HH:mm') : null,
              end: this.workingDays[day.key] ? this.workingHours[day.key].end.format('HH:mm') : null
            }
          })
          
          // 保存到数据
          this.$set(this.employeePreferences, employeeId, preferences)
          
          this.preferenceModalVisible = false
          this.$message.success('员工偏好保存成功')
        }
      })
    },
    
    // 取消编辑
    cancelPreference() {
      this.preferenceModalVisible = false
      this.resetForm()
    },
    
    // 添加特殊要求
    addRequirement() {
      this.specialRequirements.push({
        type: 'other',
        description: ''
      })
    },
    
    // 移除特殊要求
    removeRequirement(index) {
      this.specialRequirements.splice(index, 1)
    },
    
    // 导入偏好
    importPreferences() {
      this.importModalVisible = true
    },
    
    // 导出偏好
    exportPreferences() {
      // 实现导出逻辑
      this.$message.success('偏好配置导出成功')
    },
    
    // 文件上传前处理
    beforeUpload(file) {
      this.fileList = [file]
      return false
    },
    
    // 移除文件
    handleRemove() {
      this.fileList = []
    },
    
    // 处理导入
    handleImport() {
      if (this.fileList.length === 0) {
        this.$message.error('请选择要导入的文件')
        return
      }
      
      // 实现导入逻辑
      this.$message.success('员工偏好导入成功')
      this.importModalVisible = false
      this.fileList = []
    },
    
    // 下载模板
    downloadTemplate() {
      // 实现模板下载
      this.$message.success('模板下载成功')
    }
  }
}
</script>

<style scoped>
.employee-preferences {
  padding: 20px;
}

.preferences-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.preferences-header h3 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.preferences-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.preferences-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.employee-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preference-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preference-section h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.week-schedule {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.day-schedule {
  display: flex;
  align-items: center;
  gap: 12px;
}

.day-label {
  width: 40px;
  font-size: 12px;
  color: #666;
}

.time-slots {
  flex: 1;
}

.time-slot {
  margin-right: 4px;
  font-size: 11px;
}

.shift-tags {
  margin-top: 4px;
}

.shift-tags .ant-tag {
  margin-bottom: 4px;
}

.special-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.time-preference-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
}

.day-editor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.day-editor:last-child {
  border-bottom: none;
}

.day-header {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 120px;
}

.time-range-editor {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-separator {
  color: #666;
  font-size: 12px;
}

.special-requirements-editor {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preference-group {
  margin-bottom: 12px;
}

.preference-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preferences-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .preferences-list {
    grid-template-columns: 1fr;
  }
  
  .day-schedule {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .day-label {
    width: auto;
  }
}
</style>
