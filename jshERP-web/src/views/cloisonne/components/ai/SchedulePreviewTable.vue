<template>
  <div class="schedule-preview-table">
    <div class="table-header">
      <div class="header-info">
        <h4>排班预览表</h4>
        <p>AI生成的排班方案，支持拖拽调整</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button 
            size="small" 
            icon="edit"
            :disabled="!editable"
            @click="toggleEditMode"
          >
            {{ editMode ? '完成编辑' : '编辑模式' }}
          </a-button>
          <a-button 
            size="small" 
            icon="undo"
            :disabled="!hasChanges"
            @click="resetChanges"
          >
            撤销更改
          </a-button>
          <a-button 
            size="small" 
            icon="fullscreen"
            @click="toggleFullscreen"
          >
            {{ fullscreen ? '退出全屏' : '全屏查看' }}
          </a-button>
        </a-space>
      </div>
    </div>

    <div class="table-container" :class="{ 'fullscreen': fullscreen }">
      <!-- 时间轴头部 -->
      <div class="timeline-header">
        <div class="time-cell empty"></div>
        <div 
          v-for="date in dateRange" 
          :key="date.format('YYYY-MM-DD')"
          class="time-cell date-header"
        >
          <div class="date-main">{{ date.format('MM-DD') }}</div>
          <div class="date-sub">{{ date.format('dddd') }}</div>
        </div>
      </div>

      <!-- 排班内容 -->
      <div class="schedule-content">
        <div 
          v-for="shift in shifts" 
          :key="shift.code"
          class="shift-row"
        >
          <!-- 班次标题 -->
          <div class="shift-header" :style="{ backgroundColor: shift.color }">
            <div class="shift-name">{{ shift.name }}</div>
            <div class="shift-time">{{ shift.startTime }}-{{ shift.endTime }}</div>
          </div>

          <!-- 每日排班 -->
          <div 
            v-for="date in dateRange" 
            :key="`${shift.code}-${date.format('YYYY-MM-DD')}`"
            class="schedule-cell"
            :class="{ 
              'droppable': editMode,
              'weekend': isWeekend(date),
              'holiday': isHoliday(date)
            }"
            @drop="handleDrop($event, shift.code, date)"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
          >
            <!-- 员工卡片 -->
            <div 
              v-for="employee in getEmployeesForSlot(shift.code, date)" 
              :key="employee.id"
              class="employee-card"
              :class="{ 'dragging': draggingEmployee === employee.id }"
              :draggable="editMode"
              @dragstart="handleDragStart($event, employee, shift.code, date)"
              @dragend="handleDragEnd"
            >
              <a-avatar 
                :size="24" 
                :style="{ backgroundColor: getAvatarColor(employee.name) }"
              >
                {{ employee.name.charAt(0) }}
              </a-avatar>
              <span class="employee-name">{{ employee.name }}</span>
              
              <!-- 编辑模式下的操作按钮 -->
              <div v-if="editMode" class="card-actions">
                <a-button 
                  type="link" 
                  size="small"
                  icon="close"
                  @click="removeEmployee(employee.id, shift.code, date)"
                />
              </div>
            </div>

            <!-- 添加员工按钮 -->
            <div 
              v-if="editMode && canAddEmployee(shift.code, date)"
              class="add-employee-btn"
              @click="showAddEmployeeModal(shift.code, date)"
            >
              <a-icon type="plus" />
              <span>添加员工</span>
            </div>

            <!-- 空状态提示 -->
            <div 
              v-if="getEmployeesForSlot(shift.code, date).length === 0 && !editMode"
              class="empty-slot"
            >
              <span>无排班</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="table-footer">
      <div class="statistics">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总排班数" :value="totalSchedules" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="参与员工" :value="participatingEmployees" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均工时" :value="averageHours" :precision="1" suffix="小时" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="覆盖率" :value="coverageRate" :precision="1" suffix="%" />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 添加员工弹窗 -->
    <a-modal
      v-model="addEmployeeVisible"
      title="添加员工到排班"
      @ok="handleAddEmployee"
      @cancel="addEmployeeVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="选择员工">
          <a-select 
            v-model="selectedEmployee"
            placeholder="选择要添加的员工"
            style="width: 100%"
          >
            <a-select-option 
              v-for="emp in availableEmployees" 
              :key="emp.id" 
              :value="emp.id"
            >
              <a-avatar :size="20" :style="{ backgroundColor: getAvatarColor(emp.name) }">
                {{ emp.name.charAt(0) }}
              </a-avatar>
              <span style="margin-left: 8px">{{ emp.name }}</span>
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注">
          <a-input 
            v-model="employeeNote"
            placeholder="可选的备注信息"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'SchedulePreviewTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    editable: {
      type: Boolean,
      default: true
    },
    dateRange: {
      type: Array,
      default: () => {
        // 默认显示当前周
        const today = moment()
        const startOfWeek = today.clone().startOf('week')
        const dates = []
        for (let i = 0; i < 7; i++) {
          dates.push(startOfWeek.clone().add(i, 'days'))
        }
        return dates
      }
    }
  },
  
  data() {
    return {
      editMode: false,
      fullscreen: false,
      hasChanges: false,
      draggingEmployee: null,
      
      // 添加员工弹窗
      addEmployeeVisible: false,
      selectedEmployee: null,
      employeeNote: '',
      targetShift: null,
      targetDate: null,
      
      // 班次定义
      shifts: [
        {
          code: 'morning',
          name: '早班',
          startTime: '09:00',
          endTime: '13:00',
          color: '#fa8c16'
        },
        {
          code: 'evening',
          name: '晚班',
          startTime: '14:00',
          endTime: '18:00',
          color: '#52c41a'
        },
        {
          code: 'fullday',
          name: '全天',
          startTime: '09:00',
          endTime: '18:00',
          color: '#1890ff'
        }
      ],
      
      // 员工列表
      employees: [
        { id: 18, name: '聆花老师' },
        { id: 19, name: '梁朝伟' },
        { id: 20, name: '龚锦华' },
        { id: 21, name: '伍尚明' },
        { id: 22, name: '莫智华' }
      ],
      
      // 排班数据（模拟）
      scheduleData: {}
    }
  },
  
  computed: {
    availableEmployees() {
      // 返回可以添加到当前时段的员工
      return this.employees.filter(emp => {
        // 这里可以添加更复杂的逻辑，比如检查员工是否已经在其他班次
        return true
      })
    },
    
    totalSchedules() {
      let count = 0
      Object.values(this.scheduleData).forEach(dateData => {
        Object.values(dateData).forEach(shiftData => {
          count += shiftData.length
        })
      })
      return count
    },
    
    participatingEmployees() {
      const employeeSet = new Set()
      Object.values(this.scheduleData).forEach(dateData => {
        Object.values(dateData).forEach(shiftData => {
          shiftData.forEach(emp => employeeSet.add(emp.id))
        })
      })
      return employeeSet.size
    },
    
    averageHours() {
      if (this.participatingEmployees === 0) return 0
      
      let totalHours = 0
      Object.values(this.scheduleData).forEach(dateData => {
        Object.values(dateData).forEach((shiftData, shiftIndex) => {
          const shift = this.shifts[shiftIndex]
          const hours = this.calculateShiftHours(shift)
          totalHours += shiftData.length * hours
        })
      })
      
      return totalHours / this.participatingEmployees
    },
    
    coverageRate() {
      const totalSlots = this.dateRange.length * this.shifts.length
      const coveredSlots = Object.keys(this.scheduleData).length
      return totalSlots > 0 ? (coveredSlots / totalSlots) * 100 : 0
    }
  },
  
  mounted() {
    this.initializeScheduleData()
  },
  
  methods: {
    // 初始化排班数据
    initializeScheduleData() {
      // 生成模拟数据
      const mockData = {}
      
      this.dateRange.forEach((date, dateIndex) => {
        const dateKey = date.format('YYYY-MM-DD')
        mockData[dateKey] = {}
        
        this.shifts.forEach((shift, shiftIndex) => {
          mockData[dateKey][shift.code] = []
          
          // 随机分配一些员工
          if (Math.random() > 0.3) {
            const randomEmployee = this.employees[Math.floor(Math.random() * this.employees.length)]
            mockData[dateKey][shift.code].push({
              ...randomEmployee,
              note: ''
            })
          }
        })
      })
      
      this.scheduleData = mockData
    },
    
    // 获取指定时段的员工
    getEmployeesForSlot(shiftCode, date) {
      const dateKey = date.format('YYYY-MM-DD')
      return this.scheduleData[dateKey]?.[shiftCode] || []
    },
    
    // 切换编辑模式
    toggleEditMode() {
      this.editMode = !this.editMode
      if (!this.editMode) {
        this.$emit('change', this.scheduleData)
      }
    },
    
    // 重置更改
    resetChanges() {
      this.initializeScheduleData()
      this.hasChanges = false
    },
    
    // 切换全屏
    toggleFullscreen() {
      this.fullscreen = !this.fullscreen
    },
    
    // 拖拽开始
    handleDragStart(event, employee, shiftCode, date) {
      this.draggingEmployee = employee.id
      event.dataTransfer.setData('text/plain', JSON.stringify({
        employee,
        sourceShift: shiftCode,
        sourceDate: date.format('YYYY-MM-DD')
      }))
    },
    
    // 拖拽结束
    handleDragEnd() {
      this.draggingEmployee = null
    },
    
    // 拖拽悬停
    handleDragOver(event) {
      event.preventDefault()
    },
    
    // 拖拽进入
    handleDragEnter(event) {
      event.preventDefault()
      event.target.classList.add('drag-over')
    },
    
    // 拖拽离开
    handleDragLeave(event) {
      event.target.classList.remove('drag-over')
    },
    
    // 拖拽放置
    handleDrop(event, targetShift, targetDate) {
      event.preventDefault()
      event.target.classList.remove('drag-over')
      
      const dragData = JSON.parse(event.dataTransfer.getData('text/plain'))
      const { employee, sourceShift, sourceDate } = dragData
      
      const targetDateKey = targetDate.format('YYYY-MM-DD')
      
      // 移除原位置的员工
      if (this.scheduleData[sourceDate] && this.scheduleData[sourceDate][sourceShift]) {
        const index = this.scheduleData[sourceDate][sourceShift].findIndex(emp => emp.id === employee.id)
        if (index > -1) {
          this.scheduleData[sourceDate][sourceShift].splice(index, 1)
        }
      }
      
      // 添加到新位置
      if (!this.scheduleData[targetDateKey]) {
        this.$set(this.scheduleData, targetDateKey, {})
      }
      if (!this.scheduleData[targetDateKey][targetShift]) {
        this.$set(this.scheduleData[targetDateKey], targetShift, [])
      }
      
      this.scheduleData[targetDateKey][targetShift].push(employee)
      this.hasChanges = true
    },
    
    // 移除员工
    removeEmployee(employeeId, shiftCode, date) {
      const dateKey = date.format('YYYY-MM-DD')
      if (this.scheduleData[dateKey] && this.scheduleData[dateKey][shiftCode]) {
        const index = this.scheduleData[dateKey][shiftCode].findIndex(emp => emp.id === employeeId)
        if (index > -1) {
          this.scheduleData[dateKey][shiftCode].splice(index, 1)
          this.hasChanges = true
        }
      }
    },
    
    // 显示添加员工弹窗
    showAddEmployeeModal(shiftCode, date) {
      this.targetShift = shiftCode
      this.targetDate = date
      this.selectedEmployee = null
      this.employeeNote = ''
      this.addEmployeeVisible = true
    },
    
    // 添加员工
    handleAddEmployee() {
      if (!this.selectedEmployee || !this.targetShift || !this.targetDate) return
      
      const employee = this.employees.find(emp => emp.id === this.selectedEmployee)
      if (!employee) return
      
      const dateKey = this.targetDate.format('YYYY-MM-DD')
      
      if (!this.scheduleData[dateKey]) {
        this.$set(this.scheduleData, dateKey, {})
      }
      if (!this.scheduleData[dateKey][this.targetShift]) {
        this.$set(this.scheduleData[dateKey], this.targetShift, [])
      }
      
      this.scheduleData[dateKey][this.targetShift].push({
        ...employee,
        note: this.employeeNote
      })
      
      this.hasChanges = true
      this.addEmployeeVisible = false
    },
    
    // 检查是否可以添加员工
    canAddEmployee(shiftCode, date) {
      const employees = this.getEmployeesForSlot(shiftCode, date)
      return employees.length < 3 // 假设每个班次最多3人
    },
    
    // 工具方法
    isWeekend(date) {
      return date.day() === 0 || date.day() === 6
    },
    
    isHoliday(date) {
      // 这里可以添加节假日判断逻辑
      return false
    },
    
    getAvatarColor(name) {
      const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
      const index = name.charCodeAt(0) % colors.length
      return colors[index]
    },
    
    calculateShiftHours(shift) {
      const start = moment(shift.startTime, 'HH:mm')
      const end = moment(shift.endTime, 'HH:mm')
      return end.diff(start, 'hours', true)
    }
  }
}
</script>

<style scoped>
.schedule-preview-table {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.table-container {
  overflow-x: auto;
  max-height: 600px;
}

.table-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #fff;
  max-height: none;
}

.timeline-header {
  display: flex;
  background: #fafafa;
  border-bottom: 2px solid #e8e8e8;
  position: sticky;
  top: 0;
  z-index: 10;
}

.time-cell {
  min-width: 120px;
  padding: 12px 8px;
  text-align: center;
  border-right: 1px solid #e8e8e8;
}

.time-cell.empty {
  min-width: 100px;
  background: #f5f5f5;
}

.date-header {
  background: #fff;
}

.date-main {
  font-weight: 600;
  color: #333;
}

.date-sub {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.schedule-content {
  display: flex;
  flex-direction: column;
}

.shift-row {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
}

.shift-header {
  min-width: 100px;
  padding: 16px 8px;
  color: #fff;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.shift-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.shift-time {
  font-size: 12px;
  opacity: 0.9;
}

.schedule-cell {
  min-width: 120px;
  min-height: 80px;
  padding: 8px;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
}

.schedule-cell.weekend {
  background: #f9f9f9;
}

.schedule-cell.holiday {
  background: #fff7e6;
}

.schedule-cell.droppable {
  cursor: pointer;
}

.schedule-cell.drag-over {
  background: #e6f7ff;
  border: 2px dashed #1890ff;
}

.employee-card {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f0f9ff;
  border: 1px solid #d6e4ff;
  border-radius: 6px;
  cursor: move;
  transition: all 0.2s;
}

.employee-card:hover {
  background: #e6f7ff;
  transform: translateY(-1px);
}

.employee-card.dragging {
  opacity: 0.5;
}

.employee-name {
  font-size: 12px;
  color: #333;
  flex: 1;
}

.card-actions {
  display: flex;
  gap: 2px;
}

.add-employee-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.add-employee-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f0f9ff;
}

.empty-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ccc;
  font-size: 12px;
}

.table-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .time-cell,
  .schedule-cell {
    min-width: 100px;
  }
  
  .shift-header {
    min-width: 80px;
    padding: 12px 4px;
  }
}
</style>
