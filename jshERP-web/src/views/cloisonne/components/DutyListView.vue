<template>
  <div class="duty-list-container">
    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-card :bordered="false" class="search-card">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-input-search
              v-model="searchText"
              placeholder="搜索员工姓名"
              @search="handleSearch"
              allowClear
            />
          </a-col>
          <a-col :span="6">
            <a-select
              v-model="filterShiftType"
              placeholder="筛选班次类型"
              allowClear
              style="width: 100%"
              @change="handleFilter"
            >
              <a-select-option value="早班">早班</a-select-option>
              <a-select-option value="晚班">晚班</a-select-option>
              <a-select-option value="全天">全天</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model="filterStatus"
              placeholder="筛选状态"
              allowClear
              style="width: 100%"
              @change="handleFilter"
            >
              <a-select-option value="normal">正常</a-select-option>
              <a-select-option value="leave">请假</a-select-option>
              <a-select-option value="swap">调班</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-button @click="handleReset" icon="reload">重置</a-button>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 卡片列表区域 -->
    <div class="cards-section">
      <a-spin :spinning="loading">
        <div v-if="filteredSchedules.length === 0" class="empty-state">
          <a-empty description="暂无值班记录" />
        </div>

        <div v-else class="duty-cards-grid">
          <div
            v-for="schedule in paginatedSchedules"
            :key="schedule.id"
            class="duty-card"
            :class="getDutyCardClass(schedule)"
          >
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="employee-info">
                <div class="employee-avatar" :style="{ backgroundColor: getEmployeeColor(schedule.employeeName) }">
                  {{ schedule.employeeName.charAt(0) }}
                </div>
                <div class="employee-details">
                  <h4 class="employee-name">{{ schedule.employeeName }}</h4>
                  <span class="duty-date">{{ formatDate(schedule.dutyDate) }}</span>
                </div>
              </div>
              <div class="card-actions">
                <a-dropdown>
                  <a-button type="text" icon="more" size="small" />
                  <a-menu slot="overlay">
                    <a-menu-item @click="handleEdit(schedule)" v-has="'cloisonneDuty:edit'">
                      <a-icon type="edit" /> 编辑
                    </a-menu-item>
                    <a-menu-item @click="handleDelete(schedule)" v-has="'cloisonneDuty:delete'">
                      <a-icon type="delete" /> 删除
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
              <div class="shift-info">
                <div class="shift-badge" :class="`shift-${schedule.shiftType}`">
                  {{ schedule.shiftType }}
                </div>
                <div class="time-info">
                  <span class="time-text">{{ schedule.startTime }} - {{ schedule.endTime }}</span>
                  <span class="duration-text">{{ schedule.workHours }}小时</span>
                </div>
              </div>

              <div class="work-details">
                <div class="detail-item" v-if="schedule.workArea">
                  <a-icon type="environment" />
                  <span>{{ schedule.workArea }}</span>
                </div>
                <div class="detail-item" v-if="schedule.notes">
                  <a-icon type="file-text" />
                  <span>{{ schedule.notes }}</span>
                </div>
              </div>
            </div>

            <!-- 卡片底部 -->
            <div class="card-footer">
              <div class="status-info">
                <a-tag :color="getStatusColor(schedule.status)" size="small">
                  {{ getStatusText(schedule.status) }}
                </a-tag>
                <span class="priority-indicator" v-if="schedule.priority === 'high'">
                  <a-icon type="exclamation-circle" style="color: #ff4d4f;" />
                  重要
                </span>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section" v-if="filteredSchedules.length > 0">
      <a-pagination
        v-model="currentPage"
        :total="filteredSchedules.length"
        :page-size="pageSize"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        :page-size-options="['12', '24', '48', '96']"
        @change="handlePageChange"
        @showSizeChange="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'DutyListView',
  props: {
    schedules: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 搜索和筛选
      searchText: '',
      filterShiftType: undefined,
      filterStatus: undefined,

      // 分页
      currentPage: 1,
      pageSize: 12,

      // 员工颜色配置
      employeeColors: ['#ff7875', '#ffa940', '#fadb14', '#73d13d', '#40a9ff', '#b37feb', '#f759ab']
    }
  },
  computed: {
    // 筛选后的数据
    filteredSchedules() {
      let filtered = [...this.schedules]

      // 搜索筛选
      if (this.searchText) {
        filtered = filtered.filter(item =>
          item.employeeName.toLowerCase().includes(this.searchText.toLowerCase())
        )
      }

      // 班次类型筛选
      if (this.filterShiftType) {
        filtered = filtered.filter(item => item.shiftType === this.filterShiftType)
      }

      // 状态筛选
      if (this.filterStatus) {
        filtered = filtered.filter(item => item.status === this.filterStatus)
      }

      return filtered
    },

    // 分页后的数据
    paginatedSchedules() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredSchedules.slice(start, end)
    }
  },
  methods: {
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        'normal': 'green',
        'leave': 'orange',
        'swap': 'blue'
      }
      return colorMap[status] || 'default'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'normal': '正常',
        'leave': '请假',
        'swap': '调班'
      }
      return textMap[status] || status
    },

    // 获取员工颜色
    getEmployeeColor(employeeName) {
      const index = employeeName.charCodeAt(0) % this.employeeColors.length
      return this.employeeColors[index]
    },

    // 获取值班卡片样式类
    getDutyCardClass(schedule) {
      const classes = ['duty-card']
      if (schedule.priority === 'high') {
        classes.push('high-priority')
      }
      return classes.join(' ')
    },

    // 格式化日期
    formatDate(dateStr) {
      const date = new Date(dateStr)
      const month = date.getMonth() + 1
      const day = date.getDate()
      const weekDay = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
      return `${month}月${day}日 周${weekDay}`
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1
    },

    // 筛选处理
    handleFilter() {
      this.currentPage = 1
    },

    // 重置筛选
    handleReset() {
      this.searchText = ''
      this.filterShiftType = undefined
      this.filterStatus = undefined
      this.currentPage = 1
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page
    },

    // 页面大小变化
    handlePageSizeChange(current, size) {
      this.pageSize = size
      this.currentPage = 1
    },

    // 处理编辑
    handleEdit(record) {
      this.$emit('edit', record)
    },

    // 处理删除
    handleDelete(record) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这条值班记录吗？',
        onOk: () => {
          this.$emit('delete', record)
        }
      })
    }
  }
}
</script>

<style scoped>
.table-wrapper {
  overflow-x: auto;
}

.duty-table {
  min-width: 800px;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  color: #ff4d4f;
}

.delete-btn:hover {
  color: #ff7875;
}

/* 表格行悬停效果 */
.duty-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .duty-table {
    font-size: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .ant-divider {
    display: none;
  }
}
</style>
